import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// 全局的 TickerProvider 用于 SnackBar 动画
class _GlobalTickerProvider extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick, debugLabel: 'SnackBarUtil');
  }
}

class SnackBarUtil {
  // 配置
  final Color _successColor = const Color.fromARGB(255, 76, 175, 79);    // 成功提示颜色
  final Color _errorColor = const Color.fromARGB(255, 244, 67, 54);        // 错误提示颜色
  final Color _infoColor = const Color.fromARGB(255, 33, 149, 243);        // 普通提示颜色
  final Color _warningColor = const Color.fromARGB(255, 255, 152, 0);      // 警告提示颜色
  final Duration _defaultDuration = const Duration(seconds: 2);  // 显示时间
  final Duration _animationDuration = const Duration(milliseconds: 300);  // 动画时间

  // 全局 TickerProvider 实例
  static final _GlobalTickerProvider _tickerProvider = _GlobalTickerProvider();

  // 单例实例
  static final SnackBarUtil _instance = SnackBarUtil._internal();
  // 工厂构造函数
  factory SnackBarUtil() {
    return _instance;
  }
  // 私有构造函数
  SnackBarUtil._internal();

  // 主题配置
  SnackBarTheme _theme = SnackBarTheme();

  /// 设置全局主题
  void setTheme(SnackBarTheme theme) {
    _theme = theme;
  }

  void _showOverlay(
    BuildContext context, 
    String message, 
    Color backgroundColor, {
    Duration? duration, 
    IconData? icon,
    bool showProgressBar = true,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    Widget? action,
  }) {
    final overlayState = context.findRootAncestorStateOfType<OverlayState>();
    if (overlayState == null) return;  // 安全检查
    
    // 使用传入的duration或默认值
    final showDuration = duration ?? _defaultDuration;
    
    // 创建一个控制器来管理动画状态 - 使用全局 TickerProvider
    final animationController = AnimationController(
      vsync: _tickerProvider,
      duration: _animationDuration,
    );
    
    // 用于进度条的动画控制器 - 使用全局 TickerProvider
    final progressController = AnimationController(
      vsync: _tickerProvider,
      duration: showDuration,
    );
    
    final slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOut,
    ));
    
    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOut,
    ));
    
    // 进度动画
    final progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(progressController);
    
    // 提前声明overlayEntry变量
    late final OverlayEntry overlayEntry;
    
    // 标记是否已经关闭
    bool isClosed = false;
    
    // 安全释放动画控制器的方法
    void safeDisposeController(AnimationController controller) {
      try {
        controller.dispose();
      } catch (e) {
        // 忽略释放时的错误，可能已经被释放
        debugPrint('AnimationController dispose error: $e');
      }
    }
    
    // 关闭snackbar的函数
    void closeSnackBar({bool immediate = false}) {
      if (isClosed) return;
      isClosed = true;
      
      // 暂停进度条
      progressController.stop();
      
      if (immediate) {
        // 立即关闭，不播放动画（用于页面切换时）
        if (overlayEntry.mounted) {
          overlayEntry.remove();
          onDismissed?.call();
        }
        safeDisposeController(animationController);
        safeDisposeController(progressController);
      } else {
        // 播放退出动画
        animationController.reverse().then((_) {
          if (overlayEntry.mounted) {
            overlayEntry.remove();
            onDismissed?.call();
          }
          // 释放动画控制器
          safeDisposeController(animationController);
          safeDisposeController(progressController);
        });
      }
    }

    // 应用主题样式
    final borderRadius = _theme.borderRadius ?? BorderRadius.circular(12);
    final elevation = _theme.elevation ?? 6.0;
    final textStyle = _theme.textStyle ?? const TextStyle(
      color: Colors.white,
      fontSize: 15,
      fontWeight: FontWeight.w500,
    );
    final horizontalPadding = _theme.horizontalPadding ?? 20.0;
    final verticalPadding = _theme.verticalPadding ?? 14.0;
    
    // 创建overlay条目
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: MediaQuery.of(context).viewInsets.bottom + (_theme.bottomMargin ?? 16),
        left: _theme.horizontalMargin ?? 16,
        right: _theme.horizontalMargin ?? 16,
        child: SlideTransition(
          position: slideAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: GestureDetector(
              onTap: onTap,
              onHorizontalDragEnd: (details) {
                // 添加滑动关闭功能 - 向左或向右滑动都可以关闭
                if (details.primaryVelocity != null && 
                    details.primaryVelocity!.abs() > 200) {
                  closeSnackBar();
                }
              },
              child: Material(
                elevation: elevation,
                shadowColor: backgroundColor.withValues(alpha: 0.4),
                borderRadius: borderRadius,
                clipBehavior: Clip.antiAlias, // 确保内容不会溢出圆角
                child: Stack(
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: horizontalPadding, 
                            vertical: verticalPadding
                          ),
                          decoration: BoxDecoration(
                            color: backgroundColor,
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (icon != null) ...[
                                Padding(
                                  padding: const EdgeInsets.only(top: 2),
                                  child: Icon(icon, color: Colors.white, size: 24),
                                ),
                                const SizedBox(width: 12),
                              ],
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      message,
                                      style: textStyle,
                                    ),
                                    if (action != null) ...[
                                      const SizedBox(height: 8),
                                      action,
                                    ]
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),
                              InkWell(
                                onTap: closeSnackBar,
                                borderRadius: BorderRadius.circular(16),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Icon(
                                    Icons.close,
                                    color: Colors.white.withValues(alpha: 0.8),
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    // 进度条 - 放在Stack底部，确保与容器完美融合
                    if (showProgressBar)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: AnimatedBuilder(
                          animation: progressAnimation,
                          builder: (context, child) {
                            return ClipRRect(
                              borderRadius: borderRadius,
                              child: LinearProgressIndicator(
                                value: 1.0 - progressAnimation.value,
                                backgroundColor: Colors.transparent,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white.withValues(alpha: 0.3)),
                                minHeight: 3,
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // 使用WidgetsBinding来确保构建完成后再插入overlay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 在插入前再次检查 overlayState 是否仍然挂载
      if (!overlayState.mounted) return;
      
      overlayState.insert(overlayEntry);
      // 开始入场动画
      animationController.forward();
      // 开始进度条动画
      if (showProgressBar) {
        progressController.forward();
      }
      
      Future.delayed(showDuration, () {
        // 在移除前检查 entry 是否仍然挂载
        if (overlayEntry.mounted && !isClosed) {
          closeSnackBar();
        }
      });
    });
  }

  /// 成功提示
  /// [duration] 可选参数，自定义显示时长
  /// [showProgressBar] 是否显示倒计时进度条
  /// [onTap] 点击消息时的回调
  /// [onDismissed] 消息关闭后的回调
  /// [action] 附加操作按钮或其他小部件
  void showSuccess(
    BuildContext context, 
    String message, {
    Duration? duration,
    bool showProgressBar = true,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    Widget? action,
  }) {
    _showOverlay(
      context, 
      message, 
      _successColor, 
      duration: duration, 
      icon: Icons.check_circle_outline,
      showProgressBar: showProgressBar,
      onTap: onTap,
      onDismissed: onDismissed,
      action: action,
    );
  }

  /// 错误提示
  /// [duration] 可选参数，自定义显示时长
  /// [showProgressBar] 是否显示倒计时进度条
  /// [onTap] 点击消息时的回调
  /// [onDismissed] 消息关闭后的回调
  /// [action] 附加操作按钮或其他小部件
  void showError(
    BuildContext context, 
    String message, {
    Duration? duration,
    bool showProgressBar = true,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    Widget? action,
  }) {
    _showOverlay(
      context, 
      message, 
      _errorColor, 
      duration: duration, 
      icon: Icons.error_outline,
      showProgressBar: showProgressBar,
      onTap: onTap,
      onDismissed: onDismissed,
      action: action,
    );
  }

  /// 普通提示
  /// [duration] 可选参数，自定义显示时长
  /// [showProgressBar] 是否显示倒计时进度条
  /// [onTap] 点击消息时的回调
  /// [onDismissed] 消息关闭后的回调
  /// [action] 附加操作按钮或其他小部件
  void showInfo(
    BuildContext context, 
    String message, {
    Duration? duration,
    bool showProgressBar = true,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    Widget? action,
  }) {
    _showOverlay(
      context, 
      message, 
      _infoColor, 
      duration: duration, 
      icon: Icons.info_outline,
      showProgressBar: showProgressBar,
      onTap: onTap,
      onDismissed: onDismissed,
      action: action,
    );
  }
  
  /// 警告提示
  /// [duration] 可选参数，自定义显示时长
  /// [showProgressBar] 是否显示倒计时进度条
  /// [onTap] 点击消息时的回调
  /// [onDismissed] 消息关闭后的回调
  /// [action] 附加操作按钮或其他小部件
  void showWarning(
    BuildContext context, 
    String message, {
    Duration? duration,
    bool showProgressBar = true,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    Widget? action,
  }) {
    _showOverlay(
      context, 
      message, 
      _warningColor, 
      duration: duration, 
      icon: Icons.warning_amber_outlined,
      showProgressBar: showProgressBar,
      onTap: onTap,
      onDismissed: onDismissed,
      action: action,
    );
  }
  
  /// 自定义提示
  /// [backgroundColor] 背景颜色
  /// [icon] 图标
  /// [duration] 可选参数，自定义显示时长
  /// [showProgressBar] 是否显示倒计时进度条
  /// [onTap] 点击消息时的回调
  /// [onDismissed] 消息关闭后的回调
  /// [action] 附加操作按钮或其他小部件
  void showCustom(
    BuildContext context, 
    String message, {
    required Color backgroundColor,
    IconData? icon,
    Duration? duration,
    bool showProgressBar = true,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    Widget? action,
  }) {
    _showOverlay(
      context, 
      message, 
      backgroundColor, 
      duration: duration, 
      icon: icon,
      showProgressBar: showProgressBar,
      onTap: onTap,
      onDismissed: onDismissed,
      action: action,
    );
  }
}

/// Snackbar主题配置
class SnackBarTheme {
  final BorderRadius? borderRadius;
  final double? elevation;
  final TextStyle? textStyle;
  final double? horizontalPadding;
  final double? verticalPadding;
  final double? bottomMargin;
  final double? horizontalMargin;

  SnackBarTheme({
    this.borderRadius,
    this.elevation,
    this.textStyle,
    this.horizontalPadding,
    this.verticalPadding,
    this.bottomMargin,
    this.horizontalMargin,
  });
}

import 'package:flutter/material.dart';

/// 自动化操作定义
/// 统一管理所有操作的ID、名称、命令、分类等信息
class AutomationOperation {
  final String id;           // 唯一标识符
  final String name;         // 显示名称
  final String category;     // 所属分类
  final IconData icon;       // 分类图标
  final String command;      // dagu命令
  final String? description; // 操作描述

  const AutomationOperation({
    required this.id,
    required this.name,
    required this.category,
    required this.icon,
    required this.command,
    this.description,
  });
}

/// 操作分类定义
class OperationCategoryConfig {
  final String id;          // 分类ID
  final String name;        // 分类显示名称
  final IconData icon;      // 分类图标
  final List<String> operationIds; // 该分类下的操作ID列表

  const OperationCategoryConfig({
    required this.id,
    required this.name,
    required this.icon,
    required this.operationIds,
  });
}

/// 自动化操作配置中心
/// 单一数据源，避免重复维护
class AutomationOperations {
  
  /// 所有操作定义
  static const Map<String, AutomationOperation> _operations = {
    // 页面操作
    'new_tab': AutomationOperation(
      id: 'new_tab',
      name: '新建标签',
      category: 'page',
      icon: Icons.touch_app,
      command: 'browser_action new_tab',
      description: '在浏览器中打开新标签页',
    ),
    'close_tab': AutomationOperation(
      id: 'close_tab',
      name: '关闭标签',
      category: 'page',
      icon: Icons.touch_app,
      command: 'browser_action close_tab',
      description: '关闭当前标签页',
    ),
    'close_other_tabs': AutomationOperation(
      id: 'close_other_tabs',
      name: '关闭其他标签',
      category: 'page',
      icon: Icons.touch_app,
      command: 'browser_action close_other_tabs',
      description: '关闭除当前标签页外的所有标签页',
    ),
    'switch_tab': AutomationOperation(
      id: 'switch_tab',
      name: '切换标签',
      category: 'page',
      icon: Icons.touch_app,
      command: 'browser_action switch_tab --index=\${TAB_INDEX}',
      description: '切换到指定的标签页',
    ),
    'navigate': AutomationOperation(
      id: 'navigate',
      name: '访问网站',
      category: 'page',
      icon: Icons.touch_app,
      command: 'browser_action navigate --url=\${URL}',
      description: '导航到指定网址',
    ),
    'refresh': AutomationOperation(
      id: 'refresh',
      name: '刷新页面',
      category: 'page',
      icon: Icons.touch_app,
      command: 'browser_action refresh',
      description: '刷新当前页面',
    ),

    // 键盘操作
    'key_press': AutomationOperation(
      id: 'key_press',
      name: '键盘按键',
      category: 'keyboard',
      icon: Icons.keyboard,
      command: 'keyboard_action press --key=\${KEY}',
      description: '模拟键盘按键',
    ),
    'key_combo': AutomationOperation(
      id: 'key_combo',
      name: '组合键',
      category: 'keyboard',
      icon: Icons.keyboard,
      command: 'keyboard_action combo --keys=\${KEYS}',
      description: '模拟组合键操作',
    ),
    'key_shortcut': AutomationOperation(
      id: 'key_shortcut',
      name: '快捷键',
      category: 'keyboard',
      icon: Icons.keyboard,
      command: 'keyboard_action shortcut --shortcut=\${SHORTCUT}',
      description: '执行快捷键操作',
    ),
    'key_special': AutomationOperation(
      id: 'key_special',
      name: '特殊按键',
      category: 'keyboard',
      icon: Icons.keyboard,
      command: 'keyboard_action special --key=\${SPECIAL_KEY}',
      description: '模拟特殊按键（如F1-F12等）',
    ),

    // 等待操作
    'wait_fixed': AutomationOperation(
      id: 'wait_fixed',
      name: '固定时间等待',
      category: 'wait',
      icon: Icons.access_time,
      command: 'wait_action sleep --seconds=\${SECONDS}',
      description: '等待指定的时间',
    ),

    // 获取数据
    'get_text': AutomationOperation(
      id: 'get_text',
      name: '获取文本',
      category: 'data',
      icon: Icons.data_usage,
      command: 'data_action get_text --selector=\${SELECTOR}',
      description: '获取页面元素的文本内容',
    ),
    'get_attribute': AutomationOperation(
      id: 'get_attribute',
      name: '获取属性',
      category: 'data',
      icon: Icons.data_usage,
      command: 'data_action get_attribute --selector=\${SELECTOR} --attribute=\${ATTRIBUTE}',
      description: '获取页面元素的属性值',
    ),
    'get_link': AutomationOperation(
      id: 'get_link',
      name: '获取链接',
      category: 'data',
      icon: Icons.data_usage,
      command: 'data_action get_link --selector=\${SELECTOR}',
      description: '获取链接地址',
    ),
    'get_image': AutomationOperation(
      id: 'get_image',
      name: '获取图片',
      category: 'data',
      icon: Icons.data_usage,
      command: 'data_action get_image --selector=\${SELECTOR}',
      description: '获取图片源地址',
    ),
    'get_table': AutomationOperation(
      id: 'get_table',
      name: '获取表格数据',
      category: 'data',
      icon: Icons.data_usage,
      command: 'data_action get_table --selector=\${SELECTOR}',
      description: '获取表格数据',
    ),

    // 数据处理
    'data_transform': AutomationOperation(
      id: 'data_transform',
      name: '数据转换',
      category: 'process',
      icon: Icons.transform,
      command: 'process_action transform --input=\${INPUT} --output=\${OUTPUT}',
      description: '转换数据格式',
    ),
    'data_validate': AutomationOperation(
      id: 'data_validate',
      name: '数据验证',
      category: 'process',
      icon: Icons.transform,
      command: 'process_action validate --data=\${DATA} --rules=\${RULES}',
      description: '验证数据有效性',
    ),
    'data_filter': AutomationOperation(
      id: 'data_filter',
      name: '数据过滤',
      category: 'process',
      icon: Icons.transform,
      command: 'process_action filter --data=\${DATA} --conditions=\${CONDITIONS}',
      description: '根据条件过滤数据',
    ),
    'data_calculate': AutomationOperation(
      id: 'data_calculate',
      name: '数据计算',
      category: 'process',
      icon: Icons.transform,
      command: 'process_action calculate --formula=\${FORMULA}',
      description: '执行数据计算',
    ),
    'data_format': AutomationOperation(
      id: 'data_format',
      name: '数据格式化',
      category: 'process',
      icon: Icons.transform,
      command: 'process_action format --data=\${DATA} --format=\${FORMAT}',
      description: '格式化数据输出',
    ),

    // 环境信息
    'get_url': AutomationOperation(
      id: 'get_url',
      name: '获取URL',
      category: 'env',
      icon: Icons.info,
      command: 'env_action get_url',
      description: '获取当前页面URL',
    ),
    'get_title': AutomationOperation(
      id: 'get_title',
      name: '获取标题',
      category: 'env',
      icon: Icons.info,
      command: 'env_action get_title',
      description: '获取页面标题',
    ),
    'get_cookie': AutomationOperation(
      id: 'get_cookie',
      name: '获取Cookie',
      category: 'env',
      icon: Icons.info,
      command: 'env_action get_cookie --name=\${COOKIE_NAME}',
      description: '获取指定Cookie值',
    ),
    'get_localStorage': AutomationOperation(
      id: 'get_localStorage',
      name: '获取localStorage',
      category: 'env',
      icon: Icons.info,
      command: 'env_action get_local_storage --key=\${KEY}',
      description: '获取本地存储数据',
    ),
    'get_browser_info': AutomationOperation(
      id: 'get_browser_info',
      name: '浏览器信息',
      category: 'env',
      icon: Icons.info,
      command: 'env_action get_browser_info',
      description: '获取浏览器版本等信息',
    ),

    // 流程管理
    'flow_if': AutomationOperation(
      id: 'flow_if',
      name: '条件判断',
      category: 'flow',
      icon: Icons.account_tree,
      command: 'condition_check --condition=\${CONDITION} --expected=\${EXPECTED_VALUE} --timeout=\${TIMEOUT}',
      description: '根据条件执行步骤，使用preconditions配置',
    ),
    'flow_loop_while': AutomationOperation(
      id: 'flow_loop_while',
      name: '循环-当条件为真',
      category: 'flow',
      icon: Icons.account_tree,
      command: 'repeat_while --condition=\${CONDITION} --expected=\${EXPECTED_VALUE} --interval=\${INTERVAL_SEC} --limit=\${LIMIT}',
      description: '当条件为真时重复执行，使用repeatPolicy配置',
    ),
    'flow_loop_until': AutomationOperation(
      id: 'flow_loop_until',
      name: '循环-直到条件为真',
      category: 'flow',
      icon: Icons.account_tree,
      command: 'repeat_until --condition=\${CONDITION} --expected=\${EXPECTED_VALUE} --interval=\${INTERVAL_SEC} --limit=\${LIMIT}',
      description: '直到条件为真时停止重复，使用repeatPolicy配置',
    ),
    'flow_wait_element': AutomationOperation(
      id: 'flow_wait_element',
      name: '等待元素出现',
      category: 'flow',
      icon: Icons.account_tree,
      command: 'browser_action wait_element --selector=\${SELECTOR}',
      description: '等待页面元素出现',
    ),
    'flow_continue_on_error': AutomationOperation(
      id: 'flow_continue_on_error',
      name: '错误时继续',
      category: 'flow',
      icon: Icons.account_tree,
      command: 'echo "忽略错误继续执行"',
      description: '忽略步骤错误继续执行，使用continueOn配置',
    ),

    // 第三方工具
    'api_call': AutomationOperation(
      id: 'api_call',
      name: 'API调用',
      category: 'tool',
      icon: Icons.extension,
      command: 'api_action call --url=\${API_URL} --method=\${METHOD} --headers=\${HEADERS} --body=\${BODY}',
      description: '调用外部API接口',
    ),
    'file_operation': AutomationOperation(
      id: 'file_operation',
      name: '文件操作',
      category: 'tool',
      icon: Icons.extension,
      command: 'file_action operate --type=\${FILE_TYPE} --path=\${FILE_PATH} --operation=\${OPERATION}',
      description: '文件读写操作',
    ),
    'db_operation': AutomationOperation(
      id: 'db_operation',
      name: '数据库操作',
      category: 'tool',
      icon: Icons.extension,
      command: 'db_action query --connection=\${DB_CONNECTION} --sql=\${SQL}',
      description: '数据库查询操作',
    ),
    'send_email': AutomationOperation(
      id: 'send_email',
      name: '邮件发送',
      category: 'tool',
      icon: Icons.extension,
      command: 'mail_action send --to=\${TO} --subject=\${SUBJECT} --body=\${BODY}',
      description: '发送邮件通知',
    ),
    'push_notification': AutomationOperation(
      id: 'push_notification',
      name: '消息推送',
      category: 'tool',
      icon: Icons.extension,
      command: 'notification_action push --type=\${TYPE} --message=\${MESSAGE}',
      description: '推送消息通知',
    ),

    // 指纹浏览器专用操作
    'fingerprint_config': AutomationOperation(
      id: 'fingerprint_config',
      name: '配置浏览器指纹',
      category: 'fingerprint',
      icon: Icons.fingerprint,
      command: 'fingerprint_config --profile=\${PROFILE_ID} --ua=\${USER_AGENT} --resolution=\${RESOLUTION}',
      description: '配置浏览器指纹参数，包括UA、分辨率、WebGL等',
    ),
    'proxy_switch': AutomationOperation(
      id: 'proxy_switch',
      name: '切换代理',
      category: 'fingerprint',
      icon: Icons.swap_horiz,
      command: 'proxy_switch --proxy=\${PROXY_ID} --type=\${PROXY_TYPE}',
      description: '切换指纹浏览器的代理配置',
    ),
    'browser_profile_create': AutomationOperation(
      id: 'browser_profile_create',
      name: '创建浏览器配置',
      category: 'fingerprint',
      icon: Icons.add_box,
      command: 'browser_profile create --name=\${PROFILE_NAME} --group=\${GROUP_NAME}',
      description: '创建新的指纹浏览器配置文件',
    ),
    'browser_profile_load': AutomationOperation(
      id: 'browser_profile_load',
      name: '加载浏览器配置',
      category: 'fingerprint',
      icon: Icons.launch,
      command: 'browser_profile load --id=\${PROFILE_ID}',
      description: '加载指定的指纹浏览器配置',
    ),

    // 数据采集专用操作
    'data_extract_text': AutomationOperation(
      id: 'data_extract_text',
      name: '提取文本数据',
      category: 'data',
      icon: Icons.text_fields,
      command: 'data_extract text --selector=\${SELECTOR} --output=\${OUTPUT_FILE}',
      description: '从页面元素提取文本并保存到文件',
    ),
    'data_extract_table': AutomationOperation(
      id: 'data_extract_table',
      name: '提取表格数据',
      category: 'data',
      icon: Icons.table_chart,
      command: 'data_extract table --selector=\${TABLE_SELECTOR} --format=\${FORMAT} --output=\${OUTPUT_FILE}',
      description: '提取页面表格数据并保存为CSV或JSON格式',
    ),
    'data_extract_images': AutomationOperation(
      id: 'data_extract_images',
      name: '提取图片链接',
      category: 'data',
      icon: Icons.image,
      command: 'data_extract images --selector=\${IMG_SELECTOR} --download=\${DOWNLOAD_PATH}',
      description: '提取页面图片链接，可选择下载到本地',
    ),
    'data_monitor_changes': AutomationOperation(
      id: 'data_monitor_changes',
      name: '监控页面变化',
      category: 'data',
      icon: Icons.monitor,
      command: 'data_monitor --selector=\${SELECTOR} --interval=\${INTERVAL} --webhook=\${WEBHOOK_URL}',
      description: '监控页面元素变化，支持webhook通知',
    ),

    // 电商专用操作
    'ecommerce_product_info': AutomationOperation(
      id: 'ecommerce_product_info',
      name: '获取商品信息',
      category: 'ecommerce',
      icon: Icons.shopping_bag,
      command: 'ecommerce_extract product --url=\${PRODUCT_URL} --fields=\${FIELDS}',
      description: '提取电商商品的标题、价格、评分等信息',
    ),
    'ecommerce_price_monitor': AutomationOperation(
      id: 'ecommerce_price_monitor',
      name: '价格监控',
      category: 'ecommerce',
      icon: Icons.trending_down,
      command: 'ecommerce_monitor price --products=\${PRODUCT_LIST} --threshold=\${PRICE_THRESHOLD}',
      description: '监控商品价格变化，价格低于阈值时发送通知',
    ),
    'ecommerce_review_analysis': AutomationOperation(
      id: 'ecommerce_review_analysis',
      name: '评论分析',
      category: 'ecommerce',
      icon: Icons.rate_review,
      command: 'ecommerce_extract reviews --url=\${PRODUCT_URL} --pages=\${PAGE_COUNT} --sentiment=\${ANALYSIS_TYPE}',
      description: '提取商品评论并进行情感分析',
    ),

    // 社交媒体操作
    'social_post_monitor': AutomationOperation(
      id: 'social_post_monitor',
      name: '监控社交动态',
      category: 'social',
      icon: Icons.rss_feed,
      command: 'social_monitor posts --platform=\${PLATFORM} --keywords=\${KEYWORDS} --interval=\${INTERVAL}',
      description: '监控社交媒体平台的关键词相关动态',
    ),
    'social_auto_interact': AutomationOperation(
      id: 'social_auto_interact',
      name: '自动互动',
      category: 'social',
      icon: Icons.thumb_up,
      command: 'social_interact --action=\${ACTION} --target=\${TARGET_SELECTOR} --delay=\${DELAY}',
      description: '自动点赞、评论、分享等社交互动操作',
    ),
  };

  /// 分类配置
  static const Map<String, OperationCategoryConfig> _categories = {
    'page': OperationCategoryConfig(
      id: 'page',
      name: '页面操作',
      icon: Icons.touch_app,
      operationIds: ['new_tab', 'close_tab', 'close_other_tabs', 'switch_tab', 'navigate', 'refresh'],
    ),
    'keyboard': OperationCategoryConfig(
      id: 'keyboard',
      name: '键盘操作',
      icon: Icons.keyboard,
      operationIds: ['key_press', 'key_combo', 'key_shortcut', 'key_special'],
    ),
    'wait': OperationCategoryConfig(
      id: 'wait',
      name: '等待操作',
      icon: Icons.access_time,
      operationIds: ['wait_fixed'],
    ),
    'data': OperationCategoryConfig(
      id: 'data',
      name: '获取数据',
      icon: Icons.data_usage,
      operationIds: ['get_text', 'get_attribute', 'get_link', 'get_image', 'get_table'],
    ),
    'process': OperationCategoryConfig(
      id: 'process',
      name: '数据处理',
      icon: Icons.transform,
      operationIds: ['data_transform', 'data_validate', 'data_filter', 'data_calculate', 'data_format'],
    ),
    'env': OperationCategoryConfig(
      id: 'env',
      name: '环境信息',
      icon: Icons.info,
      operationIds: ['get_url', 'get_title', 'get_cookie', 'get_localStorage', 'get_browser_info'],
    ),
    'flow': OperationCategoryConfig(
      id: 'flow',
      name: '流程管理',
      icon: Icons.account_tree,
      operationIds: ['flow_if', 'flow_loop_while', 'flow_loop_until', 'flow_wait_element', 'flow_continue_on_error'],
    ),
    'tool': OperationCategoryConfig(
      id: 'tool',
      name: '第三方工具',
      icon: Icons.extension,
      operationIds: ['api_call', 'file_operation', 'db_operation', 'send_email', 'push_notification'],
    ),
    'fingerprint': OperationCategoryConfig(
      id: 'fingerprint',
      name: '指纹浏览器操作',
      icon: Icons.fingerprint,
      operationIds: ['fingerprint_config', 'proxy_switch', 'browser_profile_create', 'browser_profile_load'],
    ),
    'data_extract': OperationCategoryConfig(
      id: 'data_extract',
      name: '数据提取',
      icon: Icons.text_fields,
      operationIds: ['data_extract_text', 'data_extract_table', 'data_extract_images'],
    ),
    'data_monitor': OperationCategoryConfig(
      id: 'data_monitor',
      name: '数据监控',
      icon: Icons.monitor,
      operationIds: ['data_monitor_changes'],
    ),
    'ecommerce': OperationCategoryConfig(
      id: 'ecommerce',
      name: '电商操作',
      icon: Icons.shopping_bag,
      operationIds: ['ecommerce_product_info', 'ecommerce_price_monitor', 'ecommerce_review_analysis'],
    ),
    'social': OperationCategoryConfig(
      id: 'social',
      name: '社交媒体操作',
      icon: Icons.rss_feed,
      operationIds: ['social_post_monitor', 'social_auto_interact'],
    ),
  };

  /// 获取操作定义
  static AutomationOperation? getOperation(String id) => _operations[id];

  /// 获取所有操作
  static Map<String, AutomationOperation> get allOperations => _operations;

  /// 获取分类配置
  static OperationCategoryConfig? getCategory(String id) => _categories[id];

  /// 获取所有分类
  static Map<String, OperationCategoryConfig> get allCategories => _categories;

  /// 根据分类获取操作列表
  static List<AutomationOperation> getOperationsByCategory(String categoryId) {
    final category = _categories[categoryId];
    if (category == null) return [];
    
    return category.operationIds
        .map((id) => _operations[id])
        .where((op) => op != null)
        .cast<AutomationOperation>()
        .toList();
  }

  /// 根据操作ID获取命令
  static String? getCommand(String operationId) {
    return _operations[operationId]?.command;
  }

  /// 获取默认命令（当找不到匹配的操作时使用）
  static String getDefaultCommand(String operationName) {
    return 'echo "执行操作: $operationName"';
  }
} 

import 'package:flutter/material.dart';

/// 自动化操作定义
class AutomationOperation {
  final String id;           // 唯一标识符
  final String name;         // 显示名称
  final IconData icon;       // 操作图标
  final String command;      // dagu命令
  final String? description; // 操作描述

  const AutomationOperation({
    required this.id,
    required this.name,
    required this.icon,
    required this.command,
    this.description,
  });
}

/// 操作分类配置，包含分类信息和该分类下的所有操作
class OperationCategory {
  final String id;          // 分类ID
  final String name;        // 分类显示名称
  final IconData icon;      // 分类图标
  final List<AutomationOperation> operations; // 该分类下的操作列表

  const OperationCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.operations,
  });
}

/// 自动化操作配置中心
/// 统一管理所有分类和操作
class AutomationOperations {
  
  /// 所有操作分类及其操作定义
  static const List<OperationCategory> categories = [
    // 页面操作分类
    OperationCategory(
      id: 'page',
      name: '页面操作',
      icon: Icons.touch_app,
      operations: [
        AutomationOperation(
          id: 'new_tab',
          name: '新建标签',
          icon: Icons.add,
          command: 'browser_action new_tab',
          description: '在浏览器中打开新标签页',
        ),
        AutomationOperation(
          id: 'close_tab',
          name: '关闭标签',
          icon: Icons.close,
          command: 'browser_action close_tab',
          description: '关闭当前标签页',
        ),
        // AutomationOperation(
        //   id: 'close_other_tabs',
        //   name: '关闭其他标签',
        //   icon: Icons.clear_all,
        //   command: 'browser_action close_other_tabs',
        //   description: '关闭除当前标签页外的所有标签页',
        // ),
        // AutomationOperation(
        //   id: 'switch_tab',
        //   name: '切换标签',
        //   icon: Icons.tab,
        //   command: 'browser_action switch_tab --index=\${TAB_INDEX}',
        //   description: '切换到指定的标签页',
        // ),
        // AutomationOperation(
        //   id: 'navigate',
        //   name: '访问网站',
        //   icon: Icons.navigation,
        //   command: 'browser_action navigate --url=\${URL}',
        //   description: '导航到指定网址',
        // ),
        // AutomationOperation(
        //   id: 'refresh',
        //   name: '刷新页面',
        //   icon: Icons.refresh,
        //   command: 'browser_action refresh',
        //   description: '刷新当前页面',
        // ),
      ],
    ),

    // 键盘操作分类
    OperationCategory(
      id: 'keyboard',
      name: '键盘操作',
      icon: Icons.keyboard,
      operations: [
        AutomationOperation(
          id: 'key_press',
          name: '键盘按键',
          icon: Icons.keyboard_alt,
          command: 'keyboard_action press --key=\${KEY}',
          description: '模拟键盘按键',
        ),
        // AutomationOperation(
        //   id: 'key_combo',
        //   name: '组合键',
        //   icon: Icons.keyboard_control,
        //   command: 'keyboard_action combo --keys=\${KEYS}',
        //   description: '模拟组合键操作',
        // ),
        // AutomationOperation(
        //   id: 'key_shortcut',
        //   name: '快捷键',
        //   icon: Icons.keyboard_command,
        //   command: 'keyboard_action shortcut --shortcut=\${SHORTCUT}',
        //   description: '执行快捷键操作',
        // ),
        // AutomationOperation(
        //   id: 'key_special',
        //   name: '特殊按键',
        //   icon: Icons.keyboard_option,
        //   command: 'keyboard_action special --key=\${SPECIAL_KEY}',
        //   description: '模拟特殊按键（如F1-F12等）',
        // ),
      ],
    ),

    // 数据获取分类
    OperationCategory(
      id: 'data',
      name: '数据获取',
      icon: Icons.data_usage,
      operations: [
        AutomationOperation(
          id: 'get_text',
          name: '获取文本',
          icon: Icons.text_fields,
          command: 'data_action get_text --selector=\${SELECTOR}',
          description: '获取页面元素的文本内容',
        ),
        AutomationOperation(
          id: 'get_attribute',
          name: '获取属性',
          icon: Icons.list_alt,
          command: 'data_action get_attribute --selector=\${SELECTOR} --attribute=\${ATTRIBUTE}',
          description: '获取页面元素的属性值',
        ),
        AutomationOperation(
          id: 'get_link',
          name: '获取链接',
          icon: Icons.link,
          command: 'data_action get_link --selector=\${SELECTOR}',
          description: '获取链接地址',
        ),
        AutomationOperation(
          id: 'get_image',
          name: '获取图片',
          icon: Icons.image,
          command: 'data_action get_image --selector=\${SELECTOR}',
          description: '获取图片源地址',
        ),
        AutomationOperation(
          id: 'get_table',
          name: '获取表格数据',
          icon: Icons.table_chart,
          command: 'data_action get_table --selector=\${SELECTOR}',
          description: '获取表格数据',
        ),
        AutomationOperation(
          id: 'data_extract_text',
          name: '提取文本数据',
          icon: Icons.text_snippet,
          command: 'data_extract text --selector=\${SELECTOR} --output=\${OUTPUT_FILE}',
          description: '从页面元素提取文本并保存到文件',
        ),
        AutomationOperation(
          id: 'data_extract_table',
          name: '提取表格数据',
          icon: Icons.table_view,
          command: 'data_extract table --selector=\${TABLE_SELECTOR} --format=\${FORMAT} --output=\${OUTPUT_FILE}',
          description: '提取页面表格数据并保存为CSV或JSON格式',
        ),
        AutomationOperation(
          id: 'data_extract_images',
          name: '提取图片链接',
          icon: Icons.photo_library,
          command: 'data_extract images --selector=\${IMG_SELECTOR} --download=\${DOWNLOAD_PATH}',
          description: '提取页面图片链接，可选择下载到本地',
        ),
        AutomationOperation(
          id: 'data_monitor_changes',
          name: '监控页面变化',
          icon: Icons.monitor,
          command: 'data_monitor --selector=\${SELECTOR} --interval=\${INTERVAL} --webhook=\${WEBHOOK_URL}',
          description: '监控页面元素变化，支持webhook通知',
        ),
      ],
    ),

    // 数据处理分类
    OperationCategory(
      id: 'process',
      name: '数据处理',
      icon: Icons.transform,
      operations: [
        AutomationOperation(
          id: 'data_transform',
          name: '数据转换',
          icon: Icons.swap_horiz,
          command: 'process_action transform --input=\${INPUT} --output=\${OUTPUT}',
          description: '转换数据格式',
        ),
        AutomationOperation(
          id: 'data_validate',
          name: '数据验证',
          icon: Icons.verified,
          command: 'process_action validate --data=\${DATA} --rules=\${RULES}',
          description: '验证数据有效性',
        ),
        AutomationOperation(
          id: 'data_filter',
          name: '数据过滤',
          icon: Icons.filter_alt,
          command: 'process_action filter --data=\${DATA} --conditions=\${CONDITIONS}',
          description: '根据条件过滤数据',
        ),
        AutomationOperation(
          id: 'data_calculate',
          name: '数据计算',
          icon: Icons.calculate,
          command: 'process_action calculate --formula=\${FORMULA}',
          description: '执行数据计算',
        ),
        AutomationOperation(
          id: 'data_format',
          name: '数据格式化',
          icon: Icons.format_align_center,
          command: 'process_action format --data=\${DATA} --format=\${FORMAT}',
          description: '格式化数据输出',
        ),
      ],
    ),

    // 环境信息分类
    OperationCategory(
      id: 'env',
      name: '环境信息',
      icon: Icons.info,
      operations: [
        AutomationOperation(
          id: 'get_url',
          name: '获取URL',
          icon: Icons.link,
          command: 'env_action get_url',
          description: '获取当前页面URL',
        ),
        AutomationOperation(
          id: 'get_title',
          name: '获取标题',
          icon: Icons.title,
          command: 'env_action get_title',
          description: '获取页面标题',
        ),
        AutomationOperation(
          id: 'get_cookie',
          name: '获取Cookie',
          icon: Icons.cookie,
          command: 'env_action get_cookie --name=\${COOKIE_NAME}',
          description: '获取指定Cookie值',
        ),
        AutomationOperation(
          id: 'get_localStorage',
          name: '获取localStorage',
          icon: Icons.storage,
          command: 'env_action get_local_storage --key=\${KEY}',
          description: '获取本地存储数据',
        ),
        AutomationOperation(
          id: 'get_browser_info',
          name: '浏览器信息',
          icon: Icons.web,
          command: 'env_action get_browser_info',
          description: '获取浏览器版本等信息',
        ),
      ],
    ),

    // 流程管理分类
    OperationCategory(
      id: 'flow',
      name: '流程管理',
      icon: Icons.account_tree,
      operations: [
        AutomationOperation(
          id: 'flow_if',
          name: '条件判断',
          icon: Icons.alt_route,
          command: 'condition_check --condition=\${CONDITION} --expected=\${EXPECTED_VALUE} --timeout=\${TIMEOUT}',
          description: '根据条件执行步骤，使用preconditions配置',
        ),
        AutomationOperation(
          id: 'flow_loop_while',
          name: '循环-当条件为真',
          icon: Icons.loop,
          command: 'repeat_while --condition=\${CONDITION} --expected=\${EXPECTED_VALUE} --interval=\${INTERVAL_SEC} --limit=\${LIMIT}',
          description: '当条件为真时重复执行，使用repeatPolicy配置',
        ),
        AutomationOperation(
          id: 'flow_loop_until',
          name: '循环-直到条件为真',
          icon: Icons.repeat,
          command: 'repeat_until --condition=\${CONDITION} --expected=\${EXPECTED_VALUE} --interval=\${INTERVAL_SEC} --limit=\${LIMIT}',
          description: '直到条件为真时停止重复，使用repeatPolicy配置',
        ),
        AutomationOperation(
          id: 'flow_wait_element',
          name: '等待元素出现',
          icon: Icons.access_time,
          command: 'browser_action wait_element --selector=\${SELECTOR}',
          description: '等待页面元素出现',
        ),
        AutomationOperation(
          id: 'flow_continue_on_error',
          name: '错误时继续',
          icon: Icons.error_outline,
          command: 'echo "忽略错误继续执行"',
          description: '忽略步骤错误继续执行，使用continueOn配置',
        ),
      ],
    ),

    // // 第三方工具分类
    // OperationCategory(
    //   id: 'tool',
    //   name: '第三方工具',
    //   icon: Icons.extension,
    //   operations: [
    //     AutomationOperation(
    //       id: 'api_call',
    //       name: 'API调用',
    //       icon: Icons.api,
    //       command: 'api_action call --url=\${API_URL} --method=\${METHOD} --headers=\${HEADERS} --body=\${BODY}',
    //       description: '调用外部API接口',
    //     ),
    //     AutomationOperation(
    //       id: 'file_operation',
    //       name: '文件操作',
    //       icon: Icons.folder,
    //       command: 'file_action operate --type=\${FILE_TYPE} --path=\${FILE_PATH} --operation=\${OPERATION}',
    //       description: '文件读写操作',
    //     ),
    //              AutomationOperation(
    //        id: 'db_operation',
    //        name: '数据库操作',
    //        icon: Icons.storage,
    //        command: 'db_action query --connection=\${DB_CONNECTION} --sql=\${SQL}',
    //        description: '数据库查询操作',
    //      ),
    //     AutomationOperation(
    //       id: 'send_email',
    //       name: '邮件发送',
    //       icon: Icons.mail,
    //       command: 'mail_action send --to=\${TO} --subject=\${SUBJECT} --body=\${BODY}',
    //       description: '发送邮件通知',
    //     ),
    //     AutomationOperation(
    //       id: 'push_notification',
    //       name: '消息推送',
    //       icon: Icons.notifications,
    //       command: 'notification_action push --type=\${TYPE} --message=\${MESSAGE}',
    //       description: '推送消息通知',
    //     ),
    //   ],
    // ),

    // // 指纹浏览器专用操作分类
    // OperationCategory(
    //   id: 'fingerprint',
    //   name: '指纹浏览器',
    //   icon: Icons.fingerprint,
    //   operations: [
    //     AutomationOperation(
    //       id: 'fingerprint_config',
    //       name: '配置浏览器指纹',
    //       icon: Icons.settings,
    //       command: 'fingerprint_config --profile=\${PROFILE_ID} --ua=\${USER_AGENT} --resolution=\${RESOLUTION}',
    //       description: '配置浏览器指纹参数，包括UA、分辨率、WebGL等',
    //     ),
    //     AutomationOperation(
    //       id: 'proxy_switch',
    //       name: '切换代理',
    //       icon: Icons.swap_horiz,
    //       command: 'proxy_switch --proxy=\${PROXY_ID} --type=\${PROXY_TYPE}',
    //       description: '切换指纹浏览器的代理配置',
    //     ),
    //     AutomationOperation(
    //       id: 'browser_profile_create',
    //       name: '创建浏览器配置',
    //       icon: Icons.add_box,
    //       command: 'browser_profile create --name=\${PROFILE_NAME} --group=\${GROUP_NAME}',
    //       description: '创建新的指纹浏览器配置文件',
    //     ),
    //     AutomationOperation(
    //       id: 'browser_profile_load',
    //       name: '加载浏览器配置',
    //       icon: Icons.launch,
    //       command: 'browser_profile load --id=\${PROFILE_ID}',
    //       description: '加载指定的指纹浏览器配置',
    //     ),
    //   ],
    // ),

    // // 电商专用操作分类
    // OperationCategory(
    //   id: 'ecommerce',
    //   name: '电商操作',
    //   icon: Icons.shopping_bag,
    //   operations: [
    //     AutomationOperation(
    //       id: 'ecommerce_product_info',
    //       name: '获取商品信息',
    //       icon: Icons.shopping_cart,
    //       command: 'ecommerce_extract product --url=\${PRODUCT_URL} --fields=\${FIELDS}',
    //       description: '提取电商商品的标题、价格、评分等信息',
    //     ),
    //     AutomationOperation(
    //       id: 'ecommerce_price_monitor',
    //       name: '价格监控',
    //       icon: Icons.trending_down,
    //       command: 'ecommerce_monitor price --products=\${PRODUCT_LIST} --threshold=\${PRICE_THRESHOLD}',
    //       description: '监控商品价格变化，价格低于阈值时发送通知',
    //     ),
    //     AutomationOperation(
    //       id: 'ecommerce_review_analysis',
    //       name: '评论分析',
    //       icon: Icons.rate_review,
    //       command: 'ecommerce_extract reviews --url=\${PRODUCT_URL} --pages=\${PAGE_COUNT} --sentiment=\${ANALYSIS_TYPE}',
    //       description: '提取商品评论并进行情感分析',
    //     ),
    //   ],
    // ),

    // // 社交媒体操作分类
    // OperationCategory(
    //   id: 'social',
    //   name: '社交媒体',
    //   icon: Icons.share,
    //   operations: [
    //     AutomationOperation(
    //       id: 'social_post_monitor',
    //       name: '监控社交动态',
    //       icon: Icons.rss_feed,
    //       command: 'social_monitor posts --platform=\${PLATFORM} --keywords=\${KEYWORDS} --interval=\${INTERVAL}',
    //       description: '监控社交媒体平台的关键词相关动态',
    //     ),
    //     AutomationOperation(
    //       id: 'social_auto_interact',
    //       name: '自动互动',
    //       icon: Icons.thumb_up,
    //       command: 'social_interact --action=\${ACTION} --target=\${TARGET_SELECTOR} --delay=\${DELAY}',
    //       description: '自动点赞、评论、分享等社交互动操作',
    //     ),
    //   ],
    // ),
  ];

  // 缓存操作ID到操作的映射，提高查询性能
  static final Map<String, AutomationOperation> _operationMap = {
    for (final category in categories)
      for (final operation in category.operations)
        operation.id: operation
  };

  // 缓存分类ID到分类的映射，提高查询性能
  static final Map<String, OperationCategory> _categoryMap = {
    for (final category in categories) category.id: category
  };

  /// 获取所有分类
  static List<OperationCategory> get allCategories => categories;

  /// 获取指定分类
  static OperationCategory? getCategory(String categoryId) => _categoryMap[categoryId];

  /// 获取指定操作
  static AutomationOperation? getOperation(String operationId) => _operationMap[operationId];

  /// 获取所有操作
  static List<AutomationOperation> get allOperations => 
      categories.expand((category) => category.operations).toList();

  /// 根据分类获取操作列表
  static List<AutomationOperation> getOperationsByCategory(String categoryId) {
    final category = getCategory(categoryId);
    return category?.operations ?? [];
  }

  /// 根据操作ID获取命令
  static String? getCommand(String operationId) {
    return getOperation(operationId)?.command;
  }

  /// 获取默认命令（当找不到匹配的操作时使用）
  static String getDefaultCommand(String operationName) {
    return 'echo "执行操作: $operationName"';
  }

  /// 搜索操作（根据名称或描述）
  static List<AutomationOperation> searchOperations(String keyword) {
    final lowerKeyword = keyword.toLowerCase();
    return allOperations.where((operation) {
      return operation.name.toLowerCase().contains(lowerKeyword) ||
          (operation.description?.toLowerCase().contains(lowerKeyword) ?? false);
    }).toList();
  }

  /// 根据分类获取分类下的操作数量
  static int getOperationCountByCategory(String categoryId) {
    final category = getCategory(categoryId);
    return category?.operations.length ?? 0;
  }
} 

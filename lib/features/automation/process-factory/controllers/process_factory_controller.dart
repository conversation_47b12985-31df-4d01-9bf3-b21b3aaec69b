import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/dagu_operation.dart';

/// 流程工厂控制器 - 管理拖拽流程的状态
/// 使用 Riverpod 的 StateProvider 实现状态共享
/// 原理：所有拖拽操作都会更新这个状态，UI会自动重新构建
final droppedItemsProvider = StateProvider<List<DaguOperation>>((ref) => []);

/// 可选：历史记录提供者 - 支持撤销/重做功能
final historyProvider = StateProvider<List<List<DaguOperation>>>((ref) => []);

/// 可选：搜索查询提供者
final searchQueryProvider = StateProvider<String>((ref) => '');

/// 工作流验证状态
final workflowValidationProvider = StateProvider<List<String>>((ref) => []);

/// 当前选中的操作项
final selectedOperationProvider = StateProvider<DaguOperation?>((ref) => null);

/// 参数提示状态
final parameterHintsProvider = StateProvider<Map<String, String>>((ref) => {});

/// 流程工厂控制器
class ProcessFactoryController extends StateNotifier<ProcessFactoryState> {
  ProcessFactoryController() : super(ProcessFactoryState.initial());

  /// 添加操作项
  void addOperation(DaguOperation operation) {
    state = state.copyWith(
      operations: [...state.operations, operation],
    );
  }

  /// 删除操作项
  void removeOperation(int index) {
    final operations = [...state.operations];
    operations.removeAt(index);
    state = state.copyWith(operations: operations);
  }

  /// 更新操作项
  void updateOperation(int index, DaguOperation operation) {
    final operations = [...state.operations];
    operations[index] = operation;
    state = state.copyWith(operations: operations);
  }

  /// 重新排序操作项
  void reorderOperations(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final operations = [...state.operations];
    final item = operations.removeAt(oldIndex);
    operations.insert(newIndex, item);
    state = state.copyWith(operations: operations);
  }

  /// 验证工作流
  List<String> validateWorkflow() {
    final errors = <String>[];
    
    for (int i = 0; i < state.operations.length; i++) {
      final operation = state.operations[i];
      
      // 检查未配置的参数
      final regex = RegExp(r'\$\{([^}]+)\}');
      final matches = regex.allMatches(operation.command);
      if (matches.isNotEmpty) {
        final params = matches.map((m) => m.group(1)!).toList();
        errors.add('步骤 ${i + 1} (${operation.name}) 包含未配置的参数: ${params.join(", ")}');
      }
      
      // 检查指纹浏览器特定验证
      if (operation.operationId.startsWith('browser_') && 
          operation.command.contains('\${URL}')) {
        errors.add('步骤 ${i + 1} (${operation.name}) 包含URL参数，请确保已正确配置');
      }
    }
    
    state = state.copyWith(validationErrors: errors);
    return errors;
  }

  /// 清空所有操作
  void clearOperations() {
    state = state.copyWith(
      operations: [],
      validationErrors: [],
    );
  }

  /// 应用模板
  void applyTemplate(List<DaguOperation> templateOperations) {
    state = state.copyWith(
      operations: templateOperations,
      validationErrors: [],
    );
  }
}

/// 流程工厂状态
class ProcessFactoryState {
  final List<DaguOperation> operations;
  final List<String> validationErrors;
  final bool isLoading;
  final String? errorMessage;

  const ProcessFactoryState({
    required this.operations,
    required this.validationErrors,
    required this.isLoading,
    this.errorMessage,
  });

  factory ProcessFactoryState.initial() {
    return const ProcessFactoryState(
      operations: [],
      validationErrors: [],
      isLoading: false,
    );
  }

  ProcessFactoryState copyWith({
    List<DaguOperation>? operations,
    List<String>? validationErrors,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ProcessFactoryState(
      operations: operations ?? this.operations,
      validationErrors: validationErrors ?? this.validationErrors,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// 流程工厂控制器Provider
final processFactoryControllerProvider = 
    StateNotifierProvider<ProcessFactoryController, ProcessFactoryState>(
  (ref) => ProcessFactoryController(),
); 

import 'package:frontend_re/features/automation/process-factory/models/dagu_operation.dart';

/// Dagu YAML生成服务
/// 根据流程步骤列表生成符合dagu规范的yaml配置文件
class DaguYamlService {
  /// 生成dagu yaml内容
  static String generateYaml({
    required List<DaguOperation> operations,
    required String workflowName,
    String? description,
    Map<String, dynamic>? env,
  }) {
    if (operations.isEmpty) {
      throw ArgumentError('操作列表不能为空');
    }

    final buffer = StringBuffer();
    
    // 工作流基本信息
    buffer.writeln('# Dagu工作流配置文件');
    buffer.writeln('# 由Flutter流程设计器自动生成');
    buffer.writeln('# 符合Dagu v1.14+标准规范');
    buffer.writeln('');
    buffer.writeln('name: $workflowName');
    
    if (description?.isNotEmpty == true) {
      buffer.writeln('description: "$description"');
    }
    
    // 环境变量
    if (env?.isNotEmpty == true) {
      buffer.writeln('');
      buffer.writeln('env:');
      env!.forEach((key, value) {
        buffer.writeln('  $key: "$value"');
      });
    }
    
    // 步骤定义
    buffer.writeln('');
    buffer.writeln('steps:');
    
    for (int i = 0; i < operations.length; i++) {
      final operation = operations[i];
      final stepName = 'step_${i + 1}_${_sanitizeName(operation.name)}';
      
      buffer.writeln('  - name: $stepName');
      buffer.writeln('    desc: "${operation.name}"');
      
      // 根据操作类型生成不同的命令
      final command = _generateCommand(operation);
      buffer.writeln('    command: $command');
      
      // 添加依赖关系（除了第一个步骤）
      if (i > 0) {
        final prevStepName = 'step_${i}_${_sanitizeName(operations[i-1].name)}';
        buffer.writeln('    depends:');
        buffer.writeln('      - $prevStepName');
      }
      
      // 根据操作类型添加特殊配置
      _addSpecialConfig(buffer, operation);
      
      // 添加分类标签
      buffer.writeln('    tags:');
      buffer.writeln('      - category:${_sanitizeName(operation.category)}');
      
      if (i < operations.length - 1) {
        buffer.writeln('');
      }
    }
    
    return buffer.toString();
  }
  
  /// 根据操作类型添加特殊配置
  static void _addSpecialConfig(StringBuffer buffer, DaguOperation operation) {
    switch (operation.operationId) {
      case 'flow_if':
        // 条件判断 - 使用preconditions
        buffer.writeln('    preconditions:');
        buffer.writeln('      - condition: "\${CONDITION}"');
        buffer.writeln('        expected: "\${EXPECTED_VALUE}"');
        break;
        
      case 'flow_loop_while':
        // While循环 - 使用repeatPolicy
        buffer.writeln('    repeatPolicy:');
        buffer.writeln('      repeat: while');
        buffer.writeln('      condition: "\${CONDITION}"');
        buffer.writeln('      expected: "\${EXPECTED_VALUE}"');
        buffer.writeln('      intervalSec: 10');
        buffer.writeln('      limit: 30');
        break;
        
      case 'flow_loop_until':
        // Until循环 - 使用repeatPolicy
        buffer.writeln('    repeatPolicy:');
        buffer.writeln('      repeat: until');
        buffer.writeln('      condition: "\${CONDITION}"');
        buffer.writeln('      expected: "\${EXPECTED_VALUE}"');
        buffer.writeln('      intervalSec: 10');
        buffer.writeln('      limit: 30');
        break;
        
      case 'flow_wait_element':
        // 等待元素 - 使用until模式
        buffer.writeln('    repeatPolicy:');
        buffer.writeln('      repeat: until');
        buffer.writeln('      exitCode: [0]');
        buffer.writeln('      intervalSec: 2');
        buffer.writeln('      limit: 30');
        break;
        
      case 'flow_continue_on_error':
        // 忽略错误继续 - 使用continueOn
        buffer.writeln('    continueOn:');
        buffer.writeln('      failure: true');
        buffer.writeln('      markSuccess: true');
        break;
        
      default:
        // 其他操作类型的特殊处理
        if (operation.name.contains('等待') || operation.name.contains('wait')) {
          // 等待类操作添加超时设置
          buffer.writeln('    timeout: 30s');
        }
        break;
    }
  }
  
  /// 根据操作生成具体的命令
  static String _generateCommand(DaguOperation operation) {
    // 检查是否还有未配置的参数
    final command = operation.command;
    final regex = RegExp(r'\$\{([^}]+)\}');
    final hasUnconfiguredParams = regex.hasMatch(command);
    
    if (hasUnconfiguredParams) {
      // 如果还有未配置的参数，用注释提示
      return 'echo "警告: 命令包含未配置的参数: $command"';
    }
    
    // 直接使用操作的命令配置
    return command;
  }
  
  /// 清理名称，使其符合yaml规范
  static String _sanitizeName(String name) {
    return name
        .replaceAll(' ', '_')
        .replaceAll('/', '_')
        .replaceAll('\\', '_')
        .replaceAll(':', '_')
        .replaceAll('*', '_')
        .replaceAll('?', '_')
        .replaceAll('"', '_')
        .replaceAll('<', '_')
        .replaceAll('>', '_')
        .replaceAll('|', '_')
        .replaceAll('-', '_')
        .toLowerCase();
  }
  
  /// 保存yaml文件到指定路径
  static Future<void> saveYamlFile({
    required String content,
    required String fileName,
    String? directory,
  }) async {
    // TODO: 实现文件保存逻辑
    // 这里需要根据你的文件保存策略来实现
    // 可能需要使用path_provider获取应用目录
    print('保存YAML文件: $fileName');
    print('内容:\n$content');
  }
  
  /// 生成示例YAML展示正确的Dagu格式
  static String generateExampleYaml() {
    return '''
# Dagu工作流示例配置
# 展示条件判断、循环和错误处理的正确用法

name: browser_automation_example
description: "浏览器自动化示例工作流"

env:
  BROWSER_TYPE: "chrome"
  TIMEOUT: "30"
  HEADLESS: "false"

steps:
  # 基础步骤
  - name: navigate_to_site
    desc: "访问目标网站"
    command: browser_action navigate --url=https://example.com
    
  # 条件判断步骤
  - name: check_login_status
    desc: "检查登录状态"
    command: browser_action check_element --selector=".user-info"
    depends:
      - navigate_to_site
    preconditions:
      - condition: "\${LOGIN_REQUIRED}"
        expected: "true"
    continueOn:
      failure: true
      
  # While循环示例
  - name: wait_for_loading
    desc: "等待页面加载完成"
    command: browser_action check_element --selector=".loading"
    depends:
      - check_login_status
    repeatPolicy:
      repeat: while
      exitCode: [0]
      intervalSec: 2
      limit: 30
      
  # Until循环示例
  - name: wait_for_data
    desc: "等待数据加载"
    command: browser_action get_text --selector=".data-container"
    depends:
      - wait_for_loading
    repeatPolicy:
      repeat: until
      condition: "\${DATA_LOADED}"
      expected: "true"
      intervalSec: 5
      limit: 12
      
  # 错误处理示例
  - name: optional_action
    desc: "可选操作"
    command: browser_action click --selector=".optional-button"
    depends:
      - wait_for_data
    continueOn:
      failure: true
      exitCode: [0, 1, 2]
      markSuccess: true
      
  # 复杂条件示例
  - name: final_action
    desc: "最终操作"
    command: browser_action submit_form
    depends:
      - optional_action
    preconditions:
      - condition: "\${FORM_VALID}"
        expected: "true"
      - condition: "`date +%u`"
        expected: "re:[1-5]"  # 工作日
    continueOn:
      failure: true
''';
  }
} 

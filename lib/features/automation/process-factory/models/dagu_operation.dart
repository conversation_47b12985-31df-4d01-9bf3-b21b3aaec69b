import 'package:flutter/material.dart';
import 'package:frontend_re/core/constants/automation_operations.dart';

/// Dagu操作项简化模型
/// 使用操作ID作为唯一标识，从统一配置获取其他信息
class DaguOperation {
  final String operationId;    // 操作ID（唯一标识）
  
  const DaguOperation({
    required this.operationId,
  });
  
  /// 从操作ID获取操作配置
  AutomationOperation? get config => AutomationOperations.getOperation(operationId);
  
  /// 获取操作名称
  String get name => config?.name ?? '未知操作';
  
  /// 获取操作命令
  String get command => config?.command ?? AutomationOperations.getDefaultCommand(name);
  
  /// 获取操作描述
  String? get description => config?.description;
  
  /// 获取操作图标
  IconData? get icon => config?.icon;
  
  /// 获取操作所属的分类
  OperationCategory? get category {
    if (config == null) return null;
    
    // 遍历所有分类找到包含此操作的分类
    for (final category in AutomationOperations.allCategories) {
      if (category.operations.any((op) => op.id == operationId)) {
        return category;
      }
    }
    return null;
  }
  
  /// 获取操作分类名称
  String get categoryName => category?.name ?? '未知分类';
  
  /// 获取操作分类ID
  String get categoryId => category?.id ?? 'unknown';
  
  /// 从操作ID创建DaguOperation
  factory DaguOperation.fromId(String operationId) {
    return DaguOperation(operationId: operationId);
  }
  
  /// 检查操作是否有效（能找到对应的配置）
  bool get isValid => config != null;
  
  /// 转换为Map，用于生成yaml
  Map<String, dynamic> toMap() {
    return {
      'id': operationId,
      'name': name,
      'command': command,
      'categoryId': categoryId,
      'categoryName': categoryName,
      if (description != null) 'description': description,
    };
  }
  
  /// 转换为用于调试的详细Map
  Map<String, dynamic> toDetailedMap() {
    return {
      'operationId': operationId,
      'name': name,
      'command': command,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'description': description,
      'isValid': isValid,
    };
  }
  
  @override
  String toString() {
    return 'DaguOperation(id: $operationId, name: $name, category: $categoryName)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DaguOperation && other.operationId == operationId;
  }
  
  @override
  int get hashCode {
    return operationId.hashCode;
  }
} 

import '../../../../core/constants/automation_operations.dart';

/// Dagu操作项简化模型
/// 使用操作ID作为唯一标识，从统一配置获取其他信息
class DaguOperation {
  final String operationId;    // 操作ID（唯一标识）
  
  const DaguOperation({
    required this.operationId,
  });
  
  /// 从操作ID获取操作配置
  AutomationOperation? get config => AutomationOperations.getOperation(operationId);
  
  /// 获取操作名称
  String get name => config?.name ?? '未知操作';
  
  /// 获取操作命令
  String get command => config?.command ?? AutomationOperations.getDefaultCommand(name);
  
  /// 获取操作分类
  String get category => config?.category ?? 'unknown';
  
  /// 获取操作分类名称
  String get categoryName => AutomationOperations.getCategory(category)?.name ?? '未知分类';
  
  /// 获取操作描述
  String? get description => config?.description;
  
  /// 从操作ID创建DaguOperation
  factory DaguOperation.fromId(String operationId) {
    return DaguOperation(operationId: operationId);
  }
  
  /// 转换为Map，用于生成yaml
  Map<String, dynamic> toMap() {
    return {
      'id': operationId,
      'name': name,
      'command': command,
      'category': category,
      if (description != null) 'description': description,
    };
  }
  
  @override
  String toString() {
    return 'DaguOperation(id: $operationId, name: $name)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DaguOperation && other.operationId == operationId;
  }
  
  @override
  int get hashCode {
    return operationId.hashCode;
  }
} 

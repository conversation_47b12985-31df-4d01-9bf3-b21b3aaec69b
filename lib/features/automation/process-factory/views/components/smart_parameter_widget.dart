import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// 智能参数配置组件
/// 自动检测命令中的参数并提供智能配置界面
class SmartParameterWidget extends HookConsumerWidget {
  final String command;
  final Function(Map<String, String>) onParametersChanged;
  final Map<String, String>? initialParameters;

  const SmartParameterWidget({
    super.key,
    required this.command,
    required this.onParametersChanged,
    this.initialParameters,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 解析命令中的参数
    final parameters = _parseParameters(command);
    final controllers = useState<Map<String, TextEditingController>>({});
    
    // 初始化控制器
    useEffect(() {
      final newControllers = <String, TextEditingController>{};
      for (final param in parameters) {
        final controller = TextEditingController(
          text: initialParameters?[param] ?? _getDefaultValue(param),
        );
        controller.addListener(() {
          final paramValues = <String, String>{};
          newControllers.forEach((key, controller) {
            paramValues[key] = controller.text;
          });
          onParametersChanged(paramValues);
        });
        newControllers[param] = controller;
      }
      controllers.value = newControllers;
      
      return () {
        for (final controller in newControllers.values) {
          controller.dispose();
        }
      };
    }, [command]);

    if (parameters.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            const Icon(Icons.tune, size: 16, color: Color(0xFF0C75F8)),
            const SizedBox(width: 8),
            const Text(
              '参数配置',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            if (parameters.length > 1) ...[
              const Spacer(),
              TextButton.icon(
                onPressed: () => _fillWithDefaults(controllers.value),
                icon: const Icon(Icons.auto_fix_high, size: 16),
                label: const Text('智能填充'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF0C75F8),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 12),
        ...parameters.map((param) => _buildParameterField(
          context,
          param,
          controllers.value[param]!,
        )).toList(),
      ],
    );
  }

  /// 解析命令中的参数
  List<String> _parseParameters(String command) {
    final regex = RegExp(r'\$\{([^}]+)\}');
    final matches = regex.allMatches(command);
    return matches.map((match) => match.group(1)!).toSet().toList();
  }

  /// 构建参数输入字段
  Widget _buildParameterField(
    BuildContext context,
    String parameter,
    TextEditingController controller,
  ) {
    final paramInfo = _getParameterInfo(parameter);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFF0C75F8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  parameter,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  paramInfo['title'] ?? '参数',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              if (paramInfo['presets'] != null)
                PopupMenuButton<String>(
                  icon: const Icon(Icons.expand_more, size: 16),
                  tooltip: '选择预设值',
                  onSelected: (value) => controller.text = value,
                  itemBuilder: (context) => (paramInfo['presets'] as List<String>)
                      .map((preset) => PopupMenuItem<String>(
                            value: preset,
                            child: Text(preset),
                          ))
                      .toList(),
                ),
            ],
          ),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            style: const TextStyle(fontSize: 13),
            decoration: InputDecoration(
              hintText: paramInfo['hint'] ?? '请输入${parameter}值',
              hintStyle: const TextStyle(
                color: Color(0xFF999999),
                fontSize: 13,
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: Color(0xFFDDDDDD)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: Color(0xFFDDDDDD)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: Color(0xFF0C75F8)),
              ),
              suffixIcon: controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, size: 16),
                      onPressed: () => controller.clear(),
                    )
                  : null,
            ),
          ),
          if (paramInfo['examples'] != null) ...[
            const SizedBox(height: 6),
            Wrap(
              spacing: 6,
              children: (paramInfo['examples'] as List<String>)
                  .map((example) => InkWell(
                        onTap: () => controller.text = example,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE3F2FD),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: const Color(0xFF0C75F8), width: 0.5),
                          ),
                          child: Text(
                            example,
                            style: const TextStyle(
                              fontSize: 11,
                              color: Color(0xFF0C75F8),
                            ),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取参数信息
  Map<String, dynamic> _getParameterInfo(String parameter) {
    switch (parameter.toUpperCase()) {
      case 'URL':
        return {
          'title': '网站地址',
          'hint': '输入要访问的网站URL',
          'examples': [
            'https://www.example.com',
            'https://www.google.com',
            'https://www.baidu.com',
          ],
          'presets': [
            'https://www.google.com',
            'https://www.baidu.com',
            'https://www.amazon.com',
            'https://www.taobao.com',
          ],
        };
      case 'SELECTOR':
        return {
          'title': '页面元素选择器',
          'hint': '输入CSS选择器或XPath',
          'examples': [
            '#login-button',
            '.product-title',
            'input[name="username"]',
            '//button[@id="submit"]',
          ],
          'presets': [
            '#login-button',
            '.search-box',
            'input[type="text"]',
            'button[type="submit"]',
          ],
        };
      case 'TEXT':
      case 'INPUT':
        return {
          'title': '输入文本',
          'hint': '输入要填写的内容',
          'examples': ['测试用户', '<EMAIL>', '123456'],
        };
      case 'SECONDS':
        return {
          'title': '等待时间(秒)',
          'hint': '输入等待的秒数',
          'examples': ['3', '5', '10'],
          'presets': ['1', '3', '5', '10', '30'],
        };
      case 'CONDITION':
        return {
          'title': '条件表达式',
          'hint': '输入判断条件',
          'examples': [
            '\${LOGIN_STATUS}',
            '`curl -s http://api/health`',
            'test -f /tmp/data.txt',
          ],
          'presets': [
            '\${LOGIN_STATUS}',
            '\${PAGE_LOADED}',
            '`pgrep chrome`',
          ],
        };
      case 'EXPECTED_VALUE':
        return {
          'title': '期望值',
          'hint': '输入期望的结果',
          'examples': ['"true"', '"healthy"', '"completed"'],
          'presets': ['"true"', '"false"', '"success"', '"completed"'],
        };
      case 'TIMEOUT':
        return {
          'title': '超时时间',
          'hint': '输入超时时间',
          'examples': ['30s', '5m', '1h'],
          'presets': ['10s', '30s', '1m', '5m'],
        };
      case 'INTERVAL_SEC':
        return {
          'title': '检查间隔(秒)',
          'hint': '输入检查间隔的秒数',
          'examples': ['5', '10', '30'],
          'presets': ['1', '5', '10', '30', '60'],
        };
      case 'LIMIT':
        return {
          'title': '最大重试次数',
          'hint': '输入最大重试次数',
          'examples': ['10', '30', '60'],
          'presets': ['5', '10', '20', '30', '60'],
        };
      default:
        return {
          'title': '参数值',
          'hint': '请输入${parameter}的值',
        };
    }
  }

  /// 获取参数默认值
  String _getDefaultValue(String parameter) {
    switch (parameter.toUpperCase()) {
      case 'SECONDS':
        return '3';
      case 'TIMEOUT':
        return '30s';
      case 'EXPECTED_VALUE':
        return '"true"';
      case 'INTERVAL_SEC':
        return '5';
      case 'LIMIT':
        return '10';
      default:
        return '';
    }
  }

  /// 智能填充默认值
  void _fillWithDefaults(Map<String, TextEditingController> controllers) {
    controllers.forEach((param, controller) {
      if (controller.text.isEmpty) {
        final info = _getParameterInfo(param);
        if (info['examples'] != null) {
          final examples = info['examples'] as List<String>;
          if (examples.isNotEmpty) {
            controller.text = examples.first;
          }
        } else {
          controller.text = _getDefaultValue(param);
        }
      }
    });
  }
} 

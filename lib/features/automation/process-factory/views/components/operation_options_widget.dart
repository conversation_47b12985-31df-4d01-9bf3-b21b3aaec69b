import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/constants/automation_operations.dart';
import 'package:frontend_re/features/automation/process-factory/models/dagu_operation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 可展开的操作选项组件
/// 使用统一配置系统实现高内聚、低耦合
class OperationOptionsWidget extends HookConsumerWidget {
  final String searchKeyword;
  
  const OperationOptionsWidget({
    super.key,
    this.searchKeyword = '',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// 使用 useState Hook 管理展开状态
    /// key: 分类ID, value: 是否展开
    final expandedCategories = useState<Set<String>>({});

    /// 从统一配置获取所有分类
    final categories = AutomationOperations.allCategories;
    
    /// 当有搜索关键词时，自动展开有匹配项的分类
    useEffect(() {
      if (searchKeyword.isNotEmpty) {
        final categoriesToExpand = <String>{};
        for (final category in categories) {
          final hasMatchingOperations = category.operations.any((operation) =>
            operation.name.toLowerCase().contains(searchKeyword.toLowerCase()) ||
            category.name.toLowerCase().contains(searchKeyword.toLowerCase()) ||
            (operation.description?.toLowerCase().contains(searchKeyword.toLowerCase()) ?? false)
          );
          if (hasMatchingOperations) {
            categoriesToExpand.add(category.id);
          }
        }
        expandedCategories.value = categoriesToExpand;
      }
      return null;
    }, [searchKeyword]);

    /// 构建单个分类项的函数
    Widget buildCategoryItem(OperationCategory category) {
      final isExpanded = expandedCategories.value.contains(category.id);
      
      /// 根据搜索关键词过滤操作项
      final operations = searchKeyword.isEmpty 
          ? category.operations
          : category.operations.where((operation) =>
              operation.name.toLowerCase().contains(searchKeyword.toLowerCase()) ||
              category.name.toLowerCase().contains(searchKeyword.toLowerCase()) ||
              (operation.description?.toLowerCase().contains(searchKeyword.toLowerCase()) ?? false)
            ).toList();
      
      /// 如果搜索后该分类没有匹配的操作项，则不显示该分类
      if (searchKeyword.isNotEmpty && operations.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: const EdgeInsets.only(bottom: 8, right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            /// 分类标题行 - 可点击展开/收起
            InkWell(
              onTap: () {
                final newExpanded = Set<String>.from(expandedCategories.value);
                if (isExpanded) {
                  newExpanded.remove(category.id);
                } else {
                  newExpanded.add(category.id);
                }
                expandedCategories.value = newExpanded;
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
                child: Row(
                  children: [
                    /// 分类图标
                    Icon(
                      category.icon,
                      size: 16,
                      color: const Color(0xFF0C75F8),
                    ),
                    const SizedBox(width: 8),
                    /// 分类名称
                    Expanded(
                      child: Text(
                        category.name,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                    /// 操作数量显示
                    Text(
                      '(${operations.length})',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF8D8E93),
                      ),
                    ),
                    const SizedBox(width: 8),
                    /// 展开/收起箭头
                    AnimatedRotation(
                      duration: const Duration(milliseconds: 200),
                      turns: isExpanded ? 0.5 : 0,
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF8D8E93),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            /// 可展开的子项目列表
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: isExpanded ? null : 0,
              child: isExpanded
                  ? Column(
                      children: operations.asMap().entries.map((entry) => 
                        _OperationItemWidget(
                          operation: entry.value,
                          index: entry.key,
                        )
                      ).toList(),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      );
    }

    /// 过滤出有内容的分类
    final visibleCategories = categories
        .map((category) => buildCategoryItem(category))
        .where((widget) => widget is! SizedBox)
        .toList();
    
    /// 如果搜索时没有匹配结果，显示提示
    if (searchKeyword.isNotEmpty && visibleCategories.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.search_off,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                '没有找到匹配的操作项',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '尝试使用其他关键词搜索',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    /// 返回所有分类项的列表
    return Column(
      children: categories.map((category) => buildCategoryItem(category)).toList(),
    );
  }
}

/// 独立的操作项组件 - 实现拖拽功能
class _OperationItemWidget extends HookWidget {
  final AutomationOperation operation;
  final int index;
  
  const _OperationItemWidget({
    required this.operation,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    /// 使用 Hook 管理鼠标悬停状态
    final isHovered = useState(false);
    
    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: Draggable<DaguOperation>(
        /// 拖拽数据：传递 DaguOperation 对象
        data: DaguOperation(operationId: operation.id),
        /// 拖拽时跟随鼠标的反馈组件
        feedback: Container(
          width: 268,
          decoration: const BoxDecoration(
            color: Color(0xFF0C75F8),
            borderRadius: BorderRadius.all(Radius.circular(50)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    /// 操作图标
                    Icon(
                      operation.icon,
                      size: 14,
                      color: const Color(0xFFFFFFFF),
                    ),
                    const SizedBox(width: 8),
                    /// 操作名称
                    Text(
                      operation.name,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Color(0xFFFFFFFF),
                      ),
                    ),
                  ],
                ),
                SvgPicture.asset(
                  'assets/svg/add_circle.svg', 
                  width: 16, 
                  height: 16,
                  colorFilter: const ColorFilter.mode(
                    Color(0xFFFFFFFF),
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        ),
        /// 拖拽时原位置显示的占位组件
        childWhenDragging: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: Color(0x00E9ECEF),
                    borderRadius: BorderRadius.all(Radius.circular(50)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            /// 操作图标
                            Icon(
                              operation.icon,
                              size: 14,
                              color: const Color(0xFFADB5BD),
                            ),
                            const SizedBox(width: 8),
                            /// 操作名称
                            Text(
                              operation.name,
                              style: const TextStyle(
                                fontSize: 13,
                                color: Color(0xFFADB5BD),
                              ),
                            ),
                          ],
                        ),
                        SvgPicture.asset(
                          'assets/svg/add_circle.svg', 
                          width: 16, 
                          height: 16,
                          colorFilter: const ColorFilter.mode(
                            Color(0xFFADB5BD),
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        /// 正常状态下的子组件
        child: InkWell(
          onTap: () {
            // TODO: 处理子项目点击事件
            print('点击了: ${operation.name} (${operation.id})');
            // 可以显示操作详情或者执行其他逻辑
            if (operation.description != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(operation.description!),
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
            decoration: const BoxDecoration(),
            child: Row(
              children: [
                Expanded(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      color: isHovered.value ? const Color(0xFFE3F2FD) : const Color(0xFFF3F4F8),
                      borderRadius: const BorderRadius.all(Radius.circular(50)),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          /// 操作项内容
                          Expanded(
                            child: Row(
                              spacing: 8,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                /// 操作图标
                                AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  child: Icon(
                                    operation.icon,
                                    size: 14,
                                    color: isHovered.value ? const Color(0xFF0C75F8) : const Color(0xFF8D8E93),
                                  ),
                                ),

                                /// 操作项名称
                                Flexible(
                                  child: AnimatedDefaultTextStyle(
                                    duration: const Duration(milliseconds: 200),
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: isHovered.value ? const Color(0xFF0C75F8) : const Color(0xFF8D8E93),
                                    ),
                                    child: Text(
                                      operation.name,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          /// 添加图标
                          SvgPicture.asset(
                            'assets/svg/add_circle.svg', 
                            width: 16, 
                            height: 16,
                            colorFilter: ColorFilter.mode(
                              isHovered.value ? const Color(0xFF0C75F8) : const Color(0xFF8D8E93),
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 

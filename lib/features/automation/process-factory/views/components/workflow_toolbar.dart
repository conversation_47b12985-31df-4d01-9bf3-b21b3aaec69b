import 'package:flutter/material.dart';
import 'package:frontend_re/features/automation/process-factory/controllers/process_factory_controller.dart';
import 'package:frontend_re/features/automation/process-factory/models/dagu_operation.dart';
import 'package:frontend_re/features/automation/process-factory/services/dagu_yaml_service.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
/// 工作流工具栏组件
/// 包含生成YAML、预览、清空等功能按钮
class WorkflowToolbar extends ConsumerWidget {
  const WorkflowToolbar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final droppedItems = ref.watch(droppedItemsProvider);
    
    return Container(
      height: 200,
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      decoration: const BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// 工具栏标题和按钮
            Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0C75F8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  '工作流工具',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                const Spacer(),
                CustomBorderButton(
                  iconPath: 'assets/svg/add.svg',
                  iconSize: 16,
                  iconColor: const Color(0xFF0C75F8),
                  onPressed: () => _showTemplateDialog(context, ref),
                ),
                const SizedBox(width: 16),
                CustomBorderButton(
                  iconPath: 'assets/svg/help.svg',
                  iconSize: 16,
                  iconColor: const Color(0xFF28A745),
                  onPressed: () => _showDaguExample(context),
                ),
                const SizedBox(width: 16),
                CustomBorderButton(
                  iconPath: 'assets/svg/help.svg',
                  iconSize: 16,
                  iconColor: const Color(0xFFF39C12),
                  onPressed: () => _showParameterGuide(context),
                ),
                const SizedBox(width: 16),
                Text(
                  '当前步骤: ${droppedItems.length}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF8D8E93),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            /// 功能按钮区域
            Expanded(
              child: Row(
                children: [
                  /// 生成YAML按钮
                  Expanded(
                    child: _buildActionButton(
                      context: context,
                      ref: ref,
                      icon: Icons.code,
                      title: '生成YAML',
                      subtitle: '导出工作流配置',
                      color: const Color(0xFF0C75F8),
                      enabled: droppedItems.isNotEmpty,
                      onTap: () => _generateYaml(context, ref),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  /// 预览按钮
                  Expanded(
                    child: _buildActionButton(
                      context: context,
                      ref: ref,
                      icon: Icons.preview,
                      title: '预览流程',
                      subtitle: '查看流程结构',
                      color: const Color(0xFF28A745),
                      enabled: droppedItems.isNotEmpty,
                      onTap: () => _previewWorkflow(context, ref),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  /// 验证按钮
                  Expanded(
                    child: _buildActionButton(
                      context: context,
                      ref: ref,
                      icon: Icons.check_circle,
                      title: '验证流程',
                      subtitle: '检查配置完整性',
                      color: const Color(0xFF17A2B8),
                      enabled: droppedItems.isNotEmpty,
                      onTap: () => _validateWorkflow(context, ref),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  /// 清空按钮
                  Expanded(
                    child: _buildActionButton(
                      context: context,
                      ref: ref,
                      icon: Icons.clear_all,
                      title: '清空流程',
                      subtitle: '重新开始设计',
                      color: const Color(0xFFDC3545),
                      enabled: droppedItems.isNotEmpty,
                      onTap: () => _clearWorkflow(context, ref),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required bool enabled,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: enabled ? onTap : null,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: enabled ? color.withValues(alpha: 0.1) : const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: enabled ? color.withValues(alpha: 0.3) : const Color(0xFFE9ECEF),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: enabled ? color : const Color(0xFFBDBDBD),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: enabled ? color : const Color(0xFFBDBDBD),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: enabled ? color.withValues(alpha: 0.7) : const Color(0xFFBDBDBD),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示模板选择对话框
  void _showTemplateDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 400,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.temple_buddhist, color: Color(0xFF0C75F8)),
                  const SizedBox(width: 8),
                  const Text(
                    '选择流程模板',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    _buildTemplateCard(
                      '指纹浏览器基础操作',
                      '包含打开浏览器、访问网站、基础交互等常用操作',
                      Icons.web,
                      () => _loadTemplate(context, ref, 'browser_basic'),
                    ),
                    const SizedBox(height: 12),
                    _buildTemplateCard(
                      '电商数据采集',
                      '自动采集商品信息、价格、评论等电商数据',
                      Icons.shopping_cart,
                      () => _loadTemplate(context, ref, 'ecommerce_scraping'),
                    ),
                    const SizedBox(height: 12),
                    _buildTemplateCard(
                      '社交媒体监控',
                      '监控社交媒体动态、评论、互动数据',
                      Icons.social_distance,
                      () => _loadTemplate(context, ref, 'social_monitoring'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTemplateCard(String title, String description, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE9ECEF)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, size: 40, color: const Color(0xFF0C75F8)),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  /// 加载模板
  void _loadTemplate(BuildContext context, WidgetRef ref, String templateId) {
    Navigator.of(context).pop();
    
    // 根据模板ID加载不同的操作序列
    List<DaguOperation> templateOperations = [];
    
    switch (templateId) {
      case 'browser_basic':
        templateOperations = [
          DaguOperation(operationId: 'new_tab'),
          DaguOperation(operationId: 'navigate'),
          DaguOperation(operationId: 'wait_fixed'),
        ];
        break;
      case 'ecommerce_scraping':
        templateOperations = [
          DaguOperation(operationId: 'new_tab'),
          DaguOperation(operationId: 'navigate'),
          DaguOperation(operationId: 'flow_wait_element'),
          DaguOperation(operationId: 'get_text'),
          DaguOperation(operationId: 'get_attribute'),
        ];
        break;
      case 'social_monitoring':
        templateOperations = [
          DaguOperation(operationId: 'new_tab'),
          DaguOperation(operationId: 'navigate'),
          DaguOperation(operationId: 'flow_loop_while'),
          DaguOperation(operationId: 'get_text'),
        ];
        break;
    }
    
    ref.read(droppedItemsProvider.notifier).state = templateOperations;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('模板已加载')),
    );
  }

  /// 生成YAML配置
  void _generateYaml(BuildContext context, WidgetRef ref) {
    final operations = ref.read(droppedItemsProvider);
    if (operations.isEmpty) return;

    try {
      final yaml = DaguYamlService.generateYaml(
        operations: operations,
        workflowName: 'browser_automation_workflow',
        description: '指纹浏览器自动化工作流程',
        env: {
          'BROWSER_TYPE': 'chrome',
          'TIMEOUT': '30',
          'HEADLESS': 'false',
        },
      );

      _showYamlDialog(context, yaml);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('生成YAML失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 显示YAML预览对话框
  void _showYamlDialog(BuildContext context, String yaml) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 800,
          height: 600,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.code, color: Color(0xFF0C75F8)),
                  const SizedBox(width: 8),
                  const Text(
                    'Dagu工作流配置',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: const Color(0xFFE9ECEF)),
                  ),
                  child: SingleChildScrollView(
                    child: SelectableText(
                      yaml,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: 实现复制到剪贴板功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('已复制到剪贴板')),
                      );
                    },
                    icon: const Icon(Icons.copy),
                    label: const Text('复制'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: 实现保存文件功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('文件保存功能待实现')),
                      );
                    },
                    icon: const Icon(Icons.download),
                    label: const Text('下载'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 预览工作流程
  void _previewWorkflow(BuildContext context, WidgetRef ref) {
    final operations = ref.read(droppedItemsProvider);
    if (operations.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.preview, color: Color(0xFF28A745)),
                  const SizedBox(width: 8),
                  const Text(
                    '工作流程预览',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: operations.length,
                  itemBuilder: (context, index) {
                    final operation = operations[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8F9FA),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE9ECEF)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: const Color(0xFF28A745),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  operation.name,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '分类: ${operation.categoryName}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF8D8E93),
                                  ),
                                ),
                                if (operation.command?.isNotEmpty == true)
                                  Text(
                                    '命令: ${operation.command}',
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: Color(0xFF0C75F8),
                                      fontFamily: 'monospace',
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 验证工作流程
  void _validateWorkflow(BuildContext context, WidgetRef ref) {
    final operations = ref.read(droppedItemsProvider);
    if (operations.isEmpty) return;

    final errors = <String>[];
    
    // 验证逻辑
    for (int i = 0; i < operations.length; i++) {
      final operation = operations[i];
      
      // 检查未配置的参数
      final regex = RegExp(r'\$\{([^}]+)\}');
      final matches = regex.allMatches(operation.command);
      if (matches.isNotEmpty) {
        final params = matches.map((m) => m.group(1)!).toList();
        errors.add('步骤 ${i + 1} (${operation.name}) 包含未配置的参数: ${params.join(", ")}');
      }
      
      // 特别检查流程控制类操作
      if (operation.operationId.startsWith('flow_')) {
        // 对于流程控制操作，检查是否仍然包含模板参数
        if (operation.command.contains('\${CONDITION}') || 
            operation.command.contains('\${EXPECTED_VALUE}')) {
          errors.add('步骤 ${i + 1} (${operation.name}) 的条件判断参数未配置完整');
        }
      }
    }
    
    _showValidationResult(context, errors);
  }

  /// 显示验证结果
  void _showValidationResult(BuildContext context, List<String> errors) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 400,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    errors.isEmpty ? Icons.check_circle : Icons.error,
                    color: errors.isEmpty ? const Color(0xFF28A745) : const Color(0xFFDC3545),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    errors.isEmpty ? '验证通过' : '发现问题',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (errors.isEmpty) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE8F5E8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.check_circle, color: Color(0xFF28A745)),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          '工作流程配置正确，所有参数已完整配置，可以正常执行。',
                          style: TextStyle(color: Color(0xFF155724)),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                const Text(
                  '请修复以下问题：',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFDC3545),
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: ListView.builder(
                    itemCount: errors.length,
                    itemBuilder: (context, index) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFF3E0),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xFFFFB74D), width: 0.5),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${index + 1}.',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Color(0xFFE65100),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                errors[index],
                                style: const TextStyle(color: Color(0xFFE65100)),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 清空工作流程
  void _clearWorkflow(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认清空'),
        content: const Text('确定要清空当前工作流程吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(droppedItemsProvider.notifier).state = [];
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('工作流程已清空')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFDC3545),
            ),
            child: const Text('确认清空', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 显示Dagu标准示例
  void _showDaguExample(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 900,
          height: 700,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.help_outline, color: Color(0xFF28A745)),
                  const SizedBox(width: 8),
                  const Text(
                    'Dagu标准YAML示例',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: const Color(0xFFE9ECEF)),
                  ),
                  child: SingleChildScrollView(
                    child: SelectableText(
                      DaguYamlService.generateExampleYaml(),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 11,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示参数指南
  void _showParameterGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 800,
          height: 600,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.info_outline, color: Color(0xFFF39C12)),
                  const SizedBox(width: 8),
                  const Text(
                    '参数配置指南',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    '参数配置说明:\n\n'
                    'CONDITION: 条件表达式\n'
                    '• \${LOGIN_STATUS} - 环境变量\n'
                    '• `curl -s http://api/health` - 命令执行\n'
                    '• test -f /tmp/data.txt - 文件检查\n\n'
                    'EXPECTED_VALUE: 期望值\n'
                    '• "true" - 字符串匹配\n'
                    '• "healthy" - 状态检查\n'
                    '• "re:[1-5]" - 正则表达式匹配\n\n'
                    '更多详细说明请查看帮助文档。',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 

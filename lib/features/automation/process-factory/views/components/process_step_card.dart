import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../models/dagu_operation.dart';

/// 简化的流程步骤卡片组件
/// 只用于ReorderableListView中的拖拽排序
class ProcessStepCardSimple extends HookWidget {
  final DaguOperation operationItem;
  final int stepIndex;
  final int reorderableIndex;
  final VoidCallback onDelete;
  final VoidCallback? onEdit;
  
  const ProcessStepCardSimple({
    required this.operationItem,
    required this.stepIndex,
    required this.reorderableIndex,
    required this.onDelete,
    this.onEdit,
    super.key,
  });



  @override
  Widget build(BuildContext context) {
    final isHovered = useState(false);
    
    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isHovered.value ? const Color(0xFF0C75F8) : const Color(0xFFE9ECEF),
            width: 1,
          ),
          boxShadow: isHovered.value ? [
            BoxShadow(
              color: const Color(0xFF0C75F8).withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          children: [
            /// 步骤序号
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFF0C75F8),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  '${stepIndex + 1}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            /// 步骤信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    operationItem.name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        operationItem.config?.icon ?? Icons.help_outline,
                        size: 12,
                        color: const Color(0xFF0C75F8),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        operationItem.categoryName,
                        style: const TextStyle(
                          fontSize: 11,
                          color: Color(0xFF0C75F8),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '步骤 ${stepIndex + 1}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF8D8E93),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            /// 删除按钮 - 悬停时显示
            if (isHovered.value)
              InkWell(
                onTap: onDelete,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.red,
                  ),
                ),
              ),
            /// 编辑按钮
            if (isHovered.value && onEdit != null)
              InkWell(
                onTap: onEdit,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.edit,
                    size: 16,
                    color: Color(0xFF0C75F8),
                  ),
                ),
              ),
            /// 拖拽手柄
            ReorderableDragStartListener(
              index: reorderableIndex,
              child: MouseRegion(
                cursor: SystemMouseCursors.move,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.drag_handle,
                    color: isHovered.value ? const Color(0xFF0C75F8) : const Color(0xFF8D8E93),
                    size: 20,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 

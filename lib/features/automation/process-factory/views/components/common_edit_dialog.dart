import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import '../../models/dagu_operation.dart';
import 'smart_parameter_widget.dart';

/// 通用编辑对话框 - 支持动态参数配置
class CommonEditDialog extends HookConsumerWidget {
  const CommonEditDialog({
    super.key, 
    required this.operation,
    required this.onSave,
  });

  final DaguOperation operation;
  final Function(DaguOperation updatedOperation) onSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 解析命令中的参数
    final parameters = _parseParameters(operation.command);
    
    // 为每个参数创建控制器
    final controllers = <String, TextEditingController>{};
    for (final param in parameters) {
      controllers[param] = useTextEditingController();
    }

    // 用于存储当前参数值的状态
    final currentParameters = useState<Map<String, String>>({});

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 600,
        constraints: const BoxConstraints(
          maxHeight: 600, // 限制最大高度
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 固定标题栏
            Container(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0C75F8),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '配置参数 - ${operation.name}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            
            // 可滚动的内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 操作描述
                    if (operation.description?.isNotEmpty == true) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F9FA),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.info_outline,
                              size: 16,
                              color: Color(0xFF0C75F8),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                operation.description!,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF666666),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    // 命令预览
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2D3748),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '命令模板:',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFFE2E8F0),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          SelectableText(
                            operation.command,
                            style: const TextStyle(
                              fontSize: 11,
                              color: Color(0xFF68D391),
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    
                    // 智能参数配置区域
                    SmartParameterWidget(
                      command: operation.command,
                      onParametersChanged: (parameters) {
                        // 参数变化时的回调处理 - 更新当前参数状态
                        currentParameters.value = parameters;
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            // 固定按钮区域
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SecondaryButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 12),
                  PrimaryButton(
                    onPressed: () {
                      // 生成配置后的操作 - 使用SmartParameterWidget的参数值
                      final configuredOperation = _generateConfiguredOperationFromParameters(
                        operation,
                        currentParameters.value,
                      );
                      onSave(configuredOperation);
                      Navigator.of(context).pop();
                    },
                    child: const Text('保存'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 解析命令中的参数
  List<String> _parseParameters(String command) {
    final regex = RegExp(r'\$\{([^}]+)\}');
    final matches = regex.allMatches(command);
    return matches.map((match) => match.group(1)!).toSet().toList()..sort();
  }

  /// 获取参数提示文本
  String _getParameterHint(String param) {
    switch (param.toUpperCase()) {
      case 'URL':
        return 'URL地址 (如: https://example.com)';
      case 'SELECTOR':
        return '页面元素选择器 (如: #id, .class, tag)';
      case 'KEY':
        return '按键名称 (如: Enter, Space, Tab)';
      case 'KEYS':
        return '组合键 (如: Ctrl+C, Alt+Tab)';
      case 'SECONDS':
        return '等待时间(秒) (如: 3, 5.5)';
      case 'TAB_INDEX':
        return '标签页索引 (如: 0, 1, 2)';
      case 'SHORTCUT':
        return '快捷键 (如: copy, paste, save)';
      case 'SPECIAL_KEY':
        return '特殊按键 (如: F1, F12, Escape)';
      case 'ATTRIBUTE':
        return '元素属性名 (如: href, src, class)';
      case 'COOKIE_NAME':
        return 'Cookie名称';
      case 'INPUT':
        return '输入数据';
      case 'OUTPUT':
        return '输出格式';
      case 'DATA':
        return '数据内容';
      case 'RULES':
        return '验证规则';
      case 'CONDITIONS':
        return '过滤条件';
      case 'FORMULA':
        return '计算公式';
      case 'FORMAT':
        return '数据格式';
      case 'CONDITION':
        return '条件表达式 (如: \${LOGIN_STATUS}, `curl -s http://api/health`, test -f /tmp/data.txt)';
      case 'EXPECTED_VALUE':
        return '期望值 (如: "true", "healthy", "completed", "re:[0-9]+")';
      case 'INTERVAL_SEC':
        return '检查间隔秒数 (如: 5, 10, 30)';
      case 'LIMIT':
        return '最大重试次数 (如: 10, 30, 60)';
      case 'EXIT_CODE':
        return '预期退出码 (如: 0, 1, [0,1,2])';
      case 'TIMEOUT':
        return '超时时间 (如: 30s, 5m, 1h)';
      default:
        return param.toLowerCase();
    }
  }

  /// 生成配置后的操作 - 从参数值生成
  DaguOperation _generateConfiguredOperationFromParameters(
    DaguOperation operation,
    Map<String, String> parameters,
  ) {
    String configuredCommand = operation.command;
    
    // 替换所有参数
    parameters.forEach((param, value) {
      if (value.trim().isNotEmpty) {
        configuredCommand = configuredCommand.replaceAll(
          '\${$param}',
          value.trim(),
        );
      }
    });
    
    // 创建新的配置后操作
    return DaguOperationConfigured(
      operationId: operation.operationId,
      configuredCommand: configuredCommand,
      parameters: Map.from(parameters),
    );
  }

  /// 生成配置后的操作 - 从控制器生成（保留兼容性）
  DaguOperation _generateConfiguredOperation(
    DaguOperation operation,
    Map<String, TextEditingController> controllers,
  ) {
    String configuredCommand = operation.command;
    
    // 替换所有参数
    controllers.forEach((param, controller) {
      final value = controller.text.trim();
      if (value.isNotEmpty) {
        configuredCommand = configuredCommand.replaceAll(
          '\${$param}',
          value,
        );
      }
    });
    
    // 创建新的配置后操作（这里需要扩展DaguOperation支持自定义命令）
    return DaguOperationConfigured(
      operationId: operation.operationId,
      configuredCommand: configuredCommand,
      parameters: Map.fromEntries(
        controllers.entries.map(
          (entry) => MapEntry(entry.key, entry.value.text.trim()),
        ),
      ),
    );
  }
}

/// 配置后的操作类 - 扩展DaguOperation
class DaguOperationConfigured extends DaguOperation {
  final String configuredCommand;
  final Map<String, String> parameters;
  
  const DaguOperationConfigured({
    required super.operationId,
    required this.configuredCommand,
    required this.parameters,
  });
  
  @override
  String get command => configuredCommand;
  
  /// 检查是否还有未配置的参数
  bool get hasUnconfiguredParameters {
    final regex = RegExp(r'\$\{([^}]+)\}');
    return regex.hasMatch(configuredCommand);
  }
  
  /// 获取未配置的参数列表
  List<String> get unconfiguredParameters {
    final regex = RegExp(r'\$\{([^}]+)\}');
    final matches = regex.allMatches(configuredCommand);
    return matches.map((match) => match.group(1)!).toSet().toList();
  }
  
  @override
  String toString() {
    return 'DaguOperationConfigured(id: $operationId, name: $name, configured: ${!hasUnconfiguredParameters})';
  }
}

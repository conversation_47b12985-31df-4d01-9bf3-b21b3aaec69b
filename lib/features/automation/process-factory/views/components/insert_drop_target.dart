import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../controllers/process_factory_controller.dart';
import '../../models/dagu_operation.dart';

/// 独立的插入目标组件
/// 专门处理从左侧列表拖拽插入的功能
class InsertDropTarget extends HookWidget {
  final int insertIndex;
  final List<DaguOperation> droppedItems;
  final WidgetRef ref;
  final String position;
  
  const InsertDropTarget({
    required this.insertIndex,
    required this.droppedItems,
    required this.ref,
    required this.position,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DragTarget<DaguOperation>(
      onAcceptWithDetails: (details) {
        /// 在指定位置插入新步骤
        final items = [...droppedItems];
        items.insert(insertIndex, details.data);
        ref.read(droppedItemsProvider.notifier).state = items;
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: double.infinity,
          height: isHovering ? 60 : 12,
          // margin: isHovering ? const EdgeInsets.symmetric(vertical: 4) : EdgeInsets.zero,
          decoration: BoxDecoration(
            color: isHovering 
                ? const Color(0xFFE3F2FD) 
                : Colors.transparent,
            borderRadius: isHovering ? BorderRadius.circular(8) : null,
            border: isHovering ? Border.all(
              color: const Color(0xFF0C75F8),
              width: 2,
            ) : null,
          ),
          child: isHovering ? Center(
            child: Text(
              '松开鼠标插入到$position',
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF0C75F8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ) : const SizedBox.shrink(),
        );
      },
    );
  }
} 

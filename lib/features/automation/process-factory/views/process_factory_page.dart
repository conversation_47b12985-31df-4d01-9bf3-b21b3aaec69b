import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/features/automation/process-factory/models/dagu_operation.dart';
import 'package:frontend_re/features/automation/process-factory/services/dagu_yaml_service.dart';
import 'package:frontend_re/features/automation/process-factory/views/components/common_edit_dialog.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/painters/index.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';

// 导入分离出的组件
import 'components/index.dart';


// NotchedPanelPainter 已移动到 lib/widgets/painters/notched_panel_painters.dart





/// 主页面组件 - 流程工厂页面
/// 使用 HookConsumerWidget 监听全局状态变化
/// 布局原理：左右分栏布局，左侧操作选项，右侧流程设计器
class ProcessFactoryPage extends HookConsumerWidget {
  const ProcessFactoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// 监听全局拖拽状态 - 当有新的操作项被拖拽时，页面会自动重新构建
    final droppedItems = ref.watch(droppedItemsProvider);
    
    /// 搜索框控制器和搜索关键词状态
    final searchController = useTextEditingController();
    final searchKeyword = useState<String>('');
    
      return Scaffold(
      // backgroundColor: const Color(0xFFF8F9FA),
      body: Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Row(
          children: [
            /// 左侧区域 - 带缺口的操作面板 + 返回按钮
            SizedBox(
              width: 300,
              child: Stack(
                children: [
                  // 带缺口的操作面板
                  Positioned(
                    top: 0, // 为返回按钮留出空间
                    left: 0, // 为返回按钮留出空间
                    right: 0,
                    bottom: 0,
                    child: CustomPaint(
                      painter: NotchedPanelPainter(),
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Color(0x00FFFFFF),
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                        ),
                        padding: const EdgeInsets.only(
                          left: 20, // 增加左边距避开缺口
                          // right: 16,
                          top: 22, // 增加上边距避开缺口
                          // bottom: 16,
                        ),
                        child: Column(
                            children: [
                              /// 标题区域
                              Row(
                                children: [
                                  const SizedBox(width: 56),
                                  /// 标题指示器 - 蓝色圆点
                                  Container(
                                    width: 16,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF0C75F8),
                                      borderRadius: const BorderRadius.all(Radius.circular(16)),
                                      border: Border.all(color: const Color(0xFFE9ECEF), width: 1),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    '操作选项',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF333333),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 26),
                              /// 搜索框
                              Padding(
                                padding: const EdgeInsets.only(right: 16),
                                child: SizedBox(
                                  height: 36,
                                  child: TextField(
                                    controller: searchController,
                                    onChanged: (value) {
                                      searchKeyword.value = value.trim();
                                    },
                                    style: const TextStyle(fontSize: 14),
                                    decoration: InputDecoration(
                                      hintText: '搜索操作项...',
                                      hintStyle: const TextStyle(
                                        color: Color(0xFF999999),
                                        fontSize: 14,
                                      ),
                                      filled: true,
                                      fillColor: const Color(0xFFF3F4F8),
                                      contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: BorderSide.none,
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: BorderSide.none,
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: const BorderSide(
                                          color: Color(0xFF0C75F8),
                                          width: 1,
                                        ),
                                      ),
                                      prefixIcon: const Icon(
                                        Icons.search,
                                        size: 16,
                                        color: Color(0xFF8D8E93),
                                      ),
                                      suffixIcon: searchKeyword.value.isNotEmpty
                                          ? IconButton(
                                              icon: const Icon(
                                                Icons.clear,
                                                size: 16,
                                                color: Color(0xFF8D8E93),
                                              ),
                                              onPressed: () {
                                                searchController.clear();
                                                searchKeyword.value = '';
                                              },
                                            )
                                          : null,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              /// 操作选项列表 - 可滚动的展开式列表
                              Expanded(
                                child: SingleChildScrollView(
                                  child: OperationOptionsWidget(
                                    searchKeyword: searchKeyword.value,
                                  ),
                                ),
                              ),
                            ],
                          ),
                      ),
                    ),
                  ),
                  
                  // 返回按钮 - 浮动在左上角
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.arrow_back),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 16),
            
            /// 右侧容器 - 流程设计器区域
            Expanded(
              child: Column(
                children: [
                  /// 上方固定高度容器 - 工具栏区域
                  const WorkflowToolbar(),
                  
                  /// 下方自适应容器 - 流程设计器主体
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        color: Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.all(Radius.circular(20)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 50),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              /// 主要内容区域
                              if (droppedItems.isEmpty)
                                /// 空状态 - 显示拖拽提示区域
                                DragTarget<DaguOperation>(
                                  onAcceptWithDetails: (details) {
                                    ref.read(droppedItemsProvider.notifier).state = [details.data];
                                  },
                                  builder: (context, candidateData, rejectedData) {
                                    final isHovering = candidateData.isNotEmpty;
                                    return Container(
                                      width: double.infinity,
                                      height: 200,
                                      decoration: BoxDecoration(
                                        color: isHovering 
                                            ? const Color(0xFFE3F2FD) 
                                            : const Color(0xFFF8F9FA),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: isHovering 
                                              ? const Color(0xFF0C75F8) 
                                              : const Color(0xFFE9ECEF),
                                          width: isHovering ? 1 : 1,
                                        ),
                                      ),
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              isHovering ? Icons.add_circle : Icons.add_circle_outline,
                                              size: 48,
                                              color: isHovering 
                                                  ? const Color(0xFF0C75F8) 
                                                  : const Color(0xFFBDBDBD),
                                            ),
                                            const SizedBox(height: 16),
                                            Text(
                                              isHovering 
                                                  ? '松开鼠标添加第一个流程步骤' 
                                                  : '拖拽操作项到这里开始构建流程',
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: isHovering 
                                                    ? const Color(0xFF0C75F8) 
                                                    : const Color(0xFFBDBDBD),
                                                fontWeight: isHovering ? FontWeight.w500 : FontWeight.normal,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                )
                              else
                                /// 有内容状态
                                Column(
                                  children: [
                                    /// 顶部插入区域
                                    InsertDropTarget(
                                      insertIndex: 0,
                                      droppedItems: droppedItems,
                                      ref: ref,
                                      position: '开头',
                                    ),
                                    
                                    /// 可重排序列表
                                    ReorderableListView.builder(
                                      shrinkWrap: true,
                                      physics: const NeverScrollableScrollPhysics(),
                                      buildDefaultDragHandles: false,
                                      itemCount: droppedItems.length,
                                      proxyDecorator: (child, index, animation) {
                                        return AnimatedBuilder(
                                          animation: animation,
                                          builder: (context, child) {
                                            return Transform.scale(
                                              scale: 1.00,
                                              child: Transform.rotate(
                                                angle: 0.00,
                                                child: Material(
                                                  color: Colors.transparent,
                                                  elevation: 0,
                                                  borderRadius: BorderRadius.circular(12),
                                                  child: child!,
                                                ),
                                              ),
                                            );
                                          },
                                          child: child,
                                        );
                                      },
                                      itemExtent: null,
                                      padding: EdgeInsets.zero,
                                      onReorder: (int oldIndex, int newIndex) {
                                        if (oldIndex < newIndex) {
                                          newIndex -= 1;
                                        }
                                        final items = [...droppedItems];
                                        final item = items.removeAt(oldIndex);
                                        items.insert(newIndex, item);
                                        ref.read(droppedItemsProvider.notifier).state = items;
                                      },
                                      itemBuilder: (context, index) {
                                        return Column(
                                          key: ValueKey('step_wrapper_$index'),
                                          children: [
                                            ProcessStepCardSimple(
                                              key: ValueKey('step_${droppedItems[index].name}_$index'),
                                              operationItem: droppedItems[index],
                                              stepIndex: index,
                                              reorderableIndex: index,
                                              onDelete: () {
                                                final items = [...droppedItems];
                                                items.removeAt(index);
                                                ref.read(droppedItemsProvider.notifier).state = items;
                                              },
                                              onEdit: () {
                                                showDialog(
                                                  context: context,
                                                  builder: (context) => CommonEditDialog(
                                                    operation: droppedItems[index],
                                                    onSave: (updatedOperation) {
                                                      final items = [...droppedItems];
                                                      items[index] = updatedOperation;
                                                      ref.read(droppedItemsProvider.notifier).state = items;
                                                    },
                                                  ),
                                                );
                                              },
                                            ),
                                            
                                            if (index < droppedItems.length - 1)
                                              InsertDropTarget(
                                                insertIndex: index + 1,
                                                droppedItems: droppedItems,
                                                ref: ref,
                                                position: '第 ${index + 2} 个位置',
                                              ),
                                          ],
                                        );
                                      },
                                    ),
                                    
                                    /// 底部插入区域
                                    InsertDropTarget(
                                      insertIndex: droppedItems.length,
                                      droppedItems: droppedItems,
                                      ref: ref,
                                      position: '末尾',
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  // 注意：原来的工具栏方法已移至 WorkflowToolbar 组件

  // 工具栏相关方法已移至 WorkflowToolbar 组件，包括：
  // - _generateYaml
  // - _showYamlDialog
  // - _previewWorkflow
  // - _clearWorkflow
  // - _showDaguExample
  // - _showParameterGuide
  // - _buildActionButton
}
